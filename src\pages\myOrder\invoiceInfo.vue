<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '开票资料',
  },
}
</route>
<script lang="ts" setup>
import { downloadOrderInvoiceApi } from '@/service/orderApi'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { invoiceOrderStore } from '@/store/invoiceOrderStore'
import { useInvoiceInfoHook } from '@/components/invoice/invoiceInfoHook'
import {
  invoiceTempCreateApi,
  invoiceTempleApi,
  invoiceTempLoadApi,
  invoiceTempUpdateApi,
} from '@/service/invoiceTempleApi'
import { OrderDirection } from '@/enums/httpEnum'
import { Article } from '@/enums'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { msgModalStore } from '@/store/msgModalStore'

const useMsgModalStore = msgModalStore()

const useInvoiceOrderStore = invoiceOrderStore()
const { orderObjList, orderSuccess } = storeToRefs(useInvoiceOrderStore)

const agree = ref(false)
let isAdd = false
const price = ref('0')
const length = ref('0')
const userStore = useUserStore()
const isModalShow = ref(false)
const loading = ref(false)
const { userId, defaultInvoiceTempId } = storeToRefs(userStore)

onLoad((option: { length: string; price: string }) => {
  // console.log(option)
  price.value = option.price
  length.value = option.length
})

onShow(() => {
  // getInvoiceTemp()
  // 隐藏错误提示，获取不了默认发票资料，则读第一位list内容，如果第一位list内容为空，则创建
  getInvoiceTemp(true)
    .then(() => {
      isAdd = false
    })
    .catch(() => {
      getInvoiceFistId()
        .then(() => {
          // 编辑
          isAdd = false
          getInvoiceTemp()
        })
        .catch(() => {
          // 创建
          isAdd = true
        })
    })
})

const {
  model,
  form,
  validatePhoneNumber,
  validateMail,
  validateIdentificationNumber,
  validateBankCode,
} = useInvoiceInfoHook()

const rules = {
  companyName: [{ required: true, message: '请输入发票抬头', trigger: ['blur'] }],
  creditCode: [
    {
      required: true,
      validator: validateIdentificationNumber,
      message: '请输入正确纳税人识别号',
      trigger: ['blur'],
    },
  ],
  email: [
    {
      required: true,
      message: '请输入邮箱地址',
      trigger: ['blur'],
    },
    {
      validator: (rule, value, callback) => {
        return uni.$u.test.email(value)
      },
      message: '请输入正确的邮箱地址',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  contact: [{ required: true, message: '请输入联系人名称', trigger: ['blur'] }],
  contactPhone: [
    {
      required: true,
      message: '请输入联系手机',
      trigger: ['blur'],
    },
    {
      validator: validatePhoneNumber,
      message: '请输入正确的手机号',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  bankCode: [
    {
      required: false,
      validator: validateBankCode,
      message: '请输入正确银行帐号',
      trigger: ['blur'],
    },
  ],
}

const getInvoiceTemp = (hideErrorToast = false) =>
  new Promise((resolve, reject) => {
    invoiceTempLoadApi(
      {
        invoiceTempId: defaultInvoiceTempId.value,
      },
      hideErrorToast,
    )
      .then((res: any) => {
        const d = res.data
        model.contact = d.contact
        model.contactPhone = d.contactPhone
        model.companyName = d.companyName
        model.creditCode = d.creditCode
        model.email = d.email
        model.bank = d.bank
        model.bankCode = d.bankCode
        model.phone = d.phone
        model.address = d.address
        resolve(true)
      })
      .catch((error) => {
        reject(error)
      })
  })

const getInvoiceFistId = () =>
  new Promise((resolve, reject) => {
    invoiceTempleApi({
      companyName: '',
      creditCode: '',
      userId: userId.value,
      groupBy: '',
      needTotalCount: true,
      orderBy: 'invoiceTempId',
      orderDirection: OrderDirection.desc,
      pageIndex: 1,
      pageSize: 100000,
    })
      .then((res: any) => {
        if (res.data.length > 0) {
          defaultInvoiceTempId.value = res.data[0].invoiceTempId
          resolve(true)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })

const createInvoiceTemp = async () => {
  try {
    const res = await invoiceTempCreateApi({
      address: model.address,
      bank: model.bank,
      bankCode: model.bankCode,
      companyName: model.companyName,
      contact: model.contact,
      contactPhone: model.contactPhone,
      creditCode: model.creditCode,
      email: model.email,
      phone: model.phone,
      userId: userId.value,
    })
    defaultInvoiceTempId.value = res.data.invoiceTempId
    return [res.data, null]
  } catch (error) {
    console.error('创建发票模板失败:', error)
    return [null, error]
  }
}

const updateInvoiceTemp = async () => {
  try {
    const response = await invoiceTempUpdateApi({
      address: model.address,
      bank: model.bank,
      bankCode: model.bankCode,
      companyName: model.companyName,
      contact: model.contact,
      contactPhone: model.contactPhone,
      creditCode: model.creditCode,
      email: model.email,
      phone: model.phone,
      userId: userId.value,
      invoiceTempId: defaultInvoiceTempId.value,
    })
    return [response, null]
  } catch (error) {
    console.error('更新发票模板失败:', error)
    return [null, error]
  }
}

const downloadInvoice = () => {
  downloadOrderInvoiceApi({
    address: model.address,
    bank: model.bank,
    bankCode: model.bankCode,
    companyName: model.companyName,
    contact: model.contact,
    contactPhone: model.contactPhone,
    creditCode: model.creditCode,
    downloadType: 2,
    email: model.email,
    phone: model.phone,
    invoiceTempId: defaultInvoiceTempId.value,
    userId: userId.value,
    orderList: orderObjList.value,
  }).then(() => {
    useMsgModalStore
      .alert({
        title: '申请成功',
        content: '发票将在1~2个工作日内发送到您邮箱。',
        closeOnClickOverlay: false,
      })
      .then(() => {
        loading.value = false
        orderSuccess.value = true
        uni.navigateBack()
      })
      .catch(() => {
        loading.value = false
      })
  })
}

const handleSubmit = async () => {
  if (!orderSuccess.value && !loading.value) {
    loading.value = true
    form.value
      .validate()
      .then(async (valid) => {
        if (valid) {
          if (agree.value) {
            let error: null
            if (isAdd) {
              const [d, errorCreate] = await createInvoiceTemp()
              error = errorCreate
              // 将新创建的模板id赋值给默认模板id，本地存储
              defaultInvoiceTempId.value = d.invoiceTempId
            } else {
              const [, errorUpdate] = await updateInvoiceTemp()
              error = errorUpdate
            }
            if (!error) {
              downloadInvoice()
            } else {
              loading.value = false
            }
          } else {
            loading.value = false
            useMsgModalStore
              .confirm({
                title: '温馨提示',
                content: '请先勾选已阅读并同意《用户隐私协议》',
              })
              .then(() => {
                uni.pageScrollTo({
                  selector: '#agreeElement',
                })
              })
          }
        } else {
          loading.value = false
        }
      })
      .catch((error) => {
        console.log(error, 'error')
        loading.value = false
      })
  }
}

const toSelectInvoiceTemplate = () => {
  uni.navigateTo({
    url: '/pages/invoiceTemple/index?toSelect=true',
  })
}

const handleModalOk = () => {
  agree.value = true
  isModalShow.value = false
}
</script>
<template>
  <up-modal
    :show="isModalShow"
    confirmText="同意"
    showCancelButton
    closeOnClickOverlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
    selector="privacy-policy"
  >
    <PaidDisclaimer :articleIds="[Article.privacy]" />
  </up-modal>
  <view class="flex">
    <view class="o-bg-primary p-1.5"></view>
    <view class="bg-white p-4 flex-grow-1">
      <view>
        已选
        <text class="font-bold">{{ length }}</text>
        张订单，
      </view>
      <view>
        合计
        <text class="font-bold">{{ price }}</text>
        元。
      </view>
    </view>
  </view>
  <view class="flex items-center justify-end o-color-aid mt-3 text-sm pr-4">
    <view @click="toSelectInvoiceTemplate">选择其他模板</view>
    <view class="px-1">
      <up-icon name="arrow-right" size="14"></up-icon>
    </view>
  </view>
  <view class="px-4 pb-4 pt-2">
    <view class="bg-white rd-2 overflow-hidden pl-4">
      <up-form ref="form" :model="model" :rules="rules" class="mt-2 ml-2" labelWidth="100">
        <up-form-item required label="发票抬头" prop="companyName">
          <up-input
            v-model="model.companyName"
            clearable
            border="bottom"
            placeholder="企业名称"
          ></up-input>
        </up-form-item>
        <up-form-item required label="纳税人识别号" prop="creditCode">
          <up-input
            v-model="model.creditCode"
            clearable
            :maxlength="20"
            border="bottom"
            placeholder=""
          ></up-input>
        </up-form-item>
        <up-form-item required label="电子邮箱" prop="email">
          <up-input
            v-model="model.email"
            clearable
            border="bottom"
            placeholder="用于接收电子发票"
          ></up-input>
        </up-form-item>
        <up-form-item required label="联系人" prop="contact">
          <up-input v-model="model.contact" clearable border="bottom" placeholder=""></up-input>
        </up-form-item>
        <up-form-item required label="联系手机号" prop="contactPhone">
          <up-input
            v-model="model.contactPhone"
            clearable
            :maxlength="11"
            border="bottom"
            placeholder=""
          >
            <template #suffix>
              <text class="color-gray text-xs">{{ model.contactPhone.length }}/11</text>
            </template>
          </up-input>
        </up-form-item>
        <up-form-item label="开户银行" prop="bank">
          <up-input v-model="model.bank" clearable border="bottom" placeholder="选填"></up-input>
        </up-form-item>
        <up-form-item label="银行账号" prop="bankCode">
          <up-input
            v-model="model.bankCode"
            clearable
            border="bottom"
            placeholder="选填"
          ></up-input>
        </up-form-item>
        <up-form-item label="注册地址" prop="address">
          <up-input v-model="model.address" clearable border="bottom" placeholder="选填"></up-input>
        </up-form-item>
        <up-form-item label="注册电话" prop="phone">
          <up-input v-model="model.phone" clearable border="bottom" placeholder="选填"></up-input>
        </up-form-item>
      </up-form>
    </view>
  </view>
  <view
    id="agreeElement"
    class="flex justify-center items-center text-xs pb-20"
    @click="agree = !agree"
  >
    <up-checkbox
      usedAlone
      v-model:checked="agree"
      labelSize="12"
      size="14"
      label="我已阅读并同意"
    ></up-checkbox>
    <view class="o-color-primary" @click.stop="isModalShow = true">《用户隐私协议》</view>
  </view>
  <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
    <view
      :class="orderSuccess || loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
      class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rd-2"
      @click="handleSubmit"
    >
      提交开票信息
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
