<route lang="json5">
{
  layout: 'tabbar',
    style: {
    navigationBarTitleText: '商品通报管理',
    },
}
</route>

<script lang="ts" setup>

</script>

<template>
  <view class="p-3">
    <view class="flex space-x-2">
      <view class="flex flex-1 flex-col space-y-2">
        <view class="f-btn-lg o-btn-light-bg-blue-border o-shadow-blue-base center flex-col">
          <image src="/static/tabbar/product.png" mode="aspectFit" class="f-btn-icon-lg" />
          <text class="f-btn-text">
            拍照快速通报
          </text>
        </view>
        <view class="f-btn-sm o-btn-light-bg-blue-border o-shadow-blue-base center flex-1 space-x-2">
          <image src="/static/tabbar/product.png" mode="aspectFit" class="f-btn-icon-sm" />
          <text class="f-btn-text">
            批量代办通报
          </text>
        </view>
      </view>
      <view class="shrink-0 space-y-2">
        <view class="f-btn-md o-btn-light-bg-blue-border o-shadow-blue-base center flex-col">
          <image src="/static/tabbar/product.png" mode="aspectFit" class="f-btn-icon-md" />
          <text class="f-btn-text">
            条码外观检测
          </text>
        </view>
        <view class="f-btn-md o-btn-light-bg-blue-border o-shadow-blue-base center flex-col">
          <image src="/static/tabbar/product.png" mode="aspectFit" class="f-btn-icon-md" />
          <text class="f-btn-text">
            产品风险检测
          </text>
        </view>
      </view>
    </view>
    <view class="mt-6 px-3">
      <view class="flex items-center justify-between text-gray">
        <view class="shrink-0">
          条码容量占用情况：
        </view>
        <view class="flex">
          <view>已使用：21</view>
          <view>
            <text class="px-1">
              /
            </text>总量：10000
          </view>
        </view>
      </view>
      <view class="f-progress-bar" />
      <view class="mt-4 flex items-center justify-between text-gray">
        <view class="shrink-0">
          快速通报情况：
        </view>
        <view class="flex">
          <view class="text-primary">
            通报中：21
          </view>
          <view>
            <text class="px-1 text-gray">
              /
            </text><text class="text-green">
              总量：10000
            </text>
          </view>
        </view>
      </view>
      <view class="mt-4 flex items-center justify-between text-gray">
        <view class="shrink-0">
          代办通报情况：
        </view>
        <view class="flex">
          <view class="text-primary">
            通报中：21
          </view>
          <view>
            <text class="px-1 text-gray">
              /
            </text><text class="text-green">
              总量：10000
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-btn-lg {
  height: 308rpx;
}

.f-btn-md {
  @apply py-2;
  width: 272rpx;
}

.f-btn-icon-lg {
}

.f-btn-icon-md {
  $w: 146rpx;
  width: $w;
  height: $w;
}

.f-btn-icon-sm {
}
</style>
