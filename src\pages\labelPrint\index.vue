<route lang="json5">
{ style: { navigationBarTitleText: '标签印刷' } }
</route>

<script lang="ts" setup>
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { DesignImage } from '@/components/image'
import ServerSwiper from '@/components/serverSwiper.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { useCreateOrder } from '@/hooks/useCreateOrder'
import CsLongButton from '@/components/customer/csLongButton.vue'
import { Color } from '@/enums/colorEnum'

const {
  loading,
  isShowStep,
  customPrice,
  serveData,
  selectServe,
  descriptionServiceType,
  handleSubmit,
  handleSelect,
} = useCreateOrder({ orderConfirmUrl: '/pages/orderConfirm/labelPrint' })
</script>
<template>
  <server-swiper :carouses-list="DesignImage.labelPrint.carouses" />
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rd-2 flex flex-col items-center justify-center">
      <view v-if="isShowStep" class="pt-2 mb-6">
        <view class="font-bold text-sm mb-2">步骤说明：</view>
        <view
          v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType].makeStep"
          :key="index"
          class="flex text-xs"
        >
          <view class="f-index font-bold o-color-primary flex-shrink-0">{{ index + 1 }}.</view>
          <view>{{ item }}</view>
        </view>
      </view>
      <view class="flex gap-3 text-xs o-color-aid" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18"></up-icon>
        <view>{{ isShowStep ? '隐藏' : '显示' }}步骤说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16"></up-icon>
        <up-icon v-else name="arrow-down" size="16"></up-icon>
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 bg-white p-4 rd-2">
      <view class="font-bold pt-2 mb-3">选择服务：</view>
      <view id="targetElement" class="mt-4">
        <radio-group class="o-vf-radio-group" @change="handleSelect">
          <label v-for="(item, index) in serveData" :key="index" class="flex py-1">
            <radio
              :checked="item.priceTempId === selectServe"
              :color="Color.blue"
              :value="item.priceTempId.toString()"
              style="transform: scale(0.7)"
            />
            <view class="f-serve flex justify-between">
              <view class="f-server-name flex-wrap pr-2">
                <text>{{ item.tempName }}</text>
                <up-tag
                  v-if="item.label"
                  :text="item.label"
                  class="mt--0.5 pl--0.5"
                  size="mini"
                  style="transform-origin: left center; scale: 0.7"
                  type="error"
                ></up-tag>
              </view>
              <view class="shrink-0">
                {{ item.price }}
                <text>{{ item.tempUnit }}</text>
              </view>
            </view>
          </label>
        </radio-group>
      </view>
      <view class="mt-5 mb-2 text-sm font-bold">其他方案请联系客服，询价后，填写金额：</view>
      <up-form>
        <up-form-item label="协定金额" labelWidth="80">
          <up-input v-model="customPrice" :maxlength="10" border="bottom" clearable type="number">
            <template #suffix>
              <view class="text-sm">元</view>
            </template>
          </up-input>
        </up-form-item>
      </up-form>
    </view>
    <view class="p-10"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rd-2"
        @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-label {
  flex-shrink: 0;
  width: 26vw;
}

.f-index {
  width: 1.2rem;
}

.f-serve {
  $w: 560rpx;
  min-width: $w;
  max-width: $w;
}

.f-server-name {
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal !important;
}

.f-p {
  text-indent: 2em;
}
</style>
