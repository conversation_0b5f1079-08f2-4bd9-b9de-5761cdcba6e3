import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'
import { tabBar } from './src/layouts/fg-tabbar/tabbarList'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'barcode_center_wx',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^fg-(.*)': '@/components/fg-$1/fg-$1.vue',
      // 下面的是 uview-plus 的配置
      '^u--(.*)': 'uview-plus/components/u-$1/u-$1.vue',
      '^up-(.*)': 'uview-plus/components/u-$1/u-$1.vue',
      '^u-([^-].*)': 'uview-plus/components/u-$1/u-$1.vue',
      // 下面是z-paging 的配置
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  /* // 这个是微信的地址输入插件，不用
    plugins: {
    'address-form': {
      version: '1.0.2', // 插件版本
      provider: 'wx57d7ae552cbb3084',
      export: 'config.js', // 步骤3.2的配置文件
    },
  }, */
  // tabbar 的配置统一在 “./src/layouts/fg-tabbar/tabbarList.ts” 文件中
  tabBar: tabBar as any,
  condition: {
    // 模式配置，仅开发期间生效
    current: 1, // 当前激活的模式(list 的索引项)
    list: [
      {
        name: 'indexWithQuery',
        path: 'pages/index/index',
        query: 'scene=emailop%3DXGxvB',
      },
      {
        name: 'invoiceInfo',
        path: 'pages/myOrder/invoiceInfo',
        query: '',
      },
      {
        name: 'myOrder',
        path: 'pages/myOrder/index',
        query: '',
      },
      {
        name: 'myFilm', // 模式名称
        path: 'pages/myFilm/index', // 启动页面，必选
        query: '', // 启动参数，在页面的onLoad函数里面得到
      },
      {
        name: 'userPage',
        path: 'pages/userPage/index',
        query: '',
      },
      {
        name: 'orderConfirm',
        path: 'pages/orderConfirm/makeFilm',
        query: '',
      },
      {
        name: 'infoReportSuccess',
        path: 'pages/paymentSuccess/infoReport',
        query: '',
      },
      {
        name: 'agencyServiceSuccess',
        path: 'pages/paymentSuccess/agencyService',
        query: '',
      },
      {
        name: 'miniShopSuccess',
        path: 'pages/paymentSuccess/miniShop',
        query: '',
      },
      {
        name: 'defaultSuccess',
        path: 'pages/paymentSuccess/default',
        query: '',
      },
      {
        name: 'infoReport',
        path: 'pages/infoReport/index',
        query: '',
      },
      {
        name: 'order_infoReport',
        path: 'pages/orderConfirm/infoReport',
        query: '',
      },
      {
        name: 'myInfoReport',
        path: 'pages/infoReport/myInfoReport',
        query: '',
      },
      {
        name: 'myRenewal',
        path: 'pages/myAgency/index',
        query: '',
      },
      {
        name: 'submitRenewal',
        path: 'pages/myAgency/submitRenewal',
        query: '',
      },
    ],
  },
})
