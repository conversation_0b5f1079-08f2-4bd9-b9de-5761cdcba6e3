<script lang="ts" setup>
import { handleCopy } from '@/utils'

// TODO 抖音复制失败
</script>

<template>
  <view class="pt-2 mb-6">
    <view class="font-bold text-sm mb-2">通报步骤：</view>
    <view class="flex text-xs">
      <view class="f-index font-bold o-color-primary flex-shrink-0">1.</view>
      <view>按需求选择通报合适的通报服务。</view>
    </view>
    <view class="flex text-xs">
      <view class="f-index font-bold o-color-primary flex-shrink-0">2.</view>
      <view>完成付款后，用电脑打开 www.gs1helper.com 网站，并点击右上角登录按钮。</view>
    </view>
    <!--    <up-copy class="flex gap-1 justify-center items-center py-2" content="www.gs1helper.com">
      <view class="px-4 py-1 o-barcode-gray-card rd-1 ml-6">www.gs1helper.com</view>
      <up-tag size="mini" plain type="warning" text="点击复制"></up-tag>
    </up-copy>-->
    <view
      class="flex gap-1 justify-center items-center py-2"
      @click="handleCopy('www.gs1helper.com')"
    >
      <view class="px-4 py-1 o-barcode-gray-card rd-1 ml-6">www.gs1helper.com</view>
      <up-tag size="mini" plain type="warning" text="点击复制"></up-tag>
    </view>
    <view class="flex text-xs">
      <view class="f-index font-bold o-color-primary flex-shrink-0">3.</view>
      <view>完成登录后，点击页面上方 信息通报。</view>
    </view>
    <view class="flex text-xs">
      <view class="f-index font-bold o-color-primary flex-shrink-0">4.</view>
      <view>根据信息通报>批量上传，显示的步骤完成信息上传。</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
