<script setup lang="ts">
import { validatePhoneNumber } from '@/utils/tool'
import { wxparentsUserGetPhoneApi } from '@/service/systemApi'
import { useUserStore } from '@/store/user'
import { updateOrderPhoneApi } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { localPhone } = storeToRefs(userStore)
const useOrderStore = orderStore()
const { orderCode } = storeToRefs(useOrderStore)
const readOnly = ref(false)
const form = ref()
const model = reactive({
  contactPhone: localPhone.value,
})

const rules = {
  contactPhone: [
    {
      required: true,
      message: '请输入联系手机',
      trigger: ['blur'],
    },
    {
      validator: validatePhoneNumber,
      message: '请输入正确的手机号',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
}

onMounted(() => {
  if (model.contactPhone) {
    readOnly.value = true
  }
})

const getPhoneNumber = (e: any) => {
  wxparentsUserGetPhoneApi({
    code: e.detail.code,
  }).then((res) => {
    model.contactPhone = res.data
  })
}

const toSavePhone = (showToast: boolean = true) => {
  /*  form.value
    .validate()
    .then((valid) => {
      if (valid) {
        updateOrderPhoneApi({
          orderCode: orderCode.value,
          phone: model.contactPhone,
        }).then(() => {
          localPhone.value = model.contactPhone
          if (showToast) {
            uni.showToast({
              icon: 'success',
              title: '已保存',
            })
            readOnly.value = true
          }
        })
      }
    })
    .catch(() => {}) */

  if (model.contactPhone === '') {
    uni.showToast({
      icon: 'none',
      title: '手机号不能为空',
    })
  } else if (!validatePhoneNumber('', model.contactPhone, () => {})) {
    uni.showToast({
      icon: 'none',
      title: '请输入正确的手机号',
    })
  } else {
    updateOrderPhoneApi({
      orderCode: orderCode.value,
      phone: model.contactPhone,
    }).then(() => {
      localPhone.value = model.contactPhone
      if (showToast) {
        uni.showToast({
          icon: 'success',
          title: '已保存',
        })
        readOnly.value = true
      }
    })
  }
}

onUnload(() => {
  if (model.contactPhone) {
    toSavePhone(false)
  }
})
</script>
<template>
  <view id="phoneElement" class="bg-white p-4 rd-2 mt-3">
    <view class="f-context mt-2 mb-2 text-sm">
      为保证收到业务办理过程中，接收反馈通知，请留下可联系的手机号：
    </view>
    <view class="flex items-baseline gap-2">
      <view class="shrink-0">
        <text class="color-red">*</text>
        手机号
      </view>
      <up-input
        v-model="model.contactPhone"
        clearable
        :readonly="readOnly"
        :maxlength="11"
        type="number"
        :border="readOnly ? 'none' : 'bottom'"
        placeholder="请输入手机号"
      >
        <template #suffix>
          <text v-if="!readOnly" class="color-gray text-xs">
            {{ model.contactPhone.length }}/11
          </text>
        </template>
      </up-input>
      <template #right>
        <button
          v-show="readOnly"
          class="f-btn-blue text-primary shrink-0 py-1 px-2 text-xs flex justify-center items-center"
          @click="readOnly = false"
        >
          修改手机
        </button>
      </template>
    </view>
    <view
      v-show="!readOnly"
      class="p-3 mt-3 flex-grow-1 flex items-center justify-center color-white font-bold rd-2 o-bg-primary"
      @click="toSavePhone"
    >
      保存提交
    </view>
  </view>
</template>

<style scoped lang="scss">
.f-btn-blue {
  border: 1px solid rgba(22, 93, 255, 0.3);
  background-color: rgba(22, 93, 255, 0.05);
}

.f-context {
  text-indent: 2em;
}
</style>
