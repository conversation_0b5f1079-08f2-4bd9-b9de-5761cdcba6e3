<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '红包卡券',
    enablePullDownRefresh: true,
    backgroundColor: '#f0f3f8',
  },
}
</route>
<script lang="ts" setup>
import { OrderDirection } from '@/enums/httpEnum'
import { couponPageApi } from '@/service/orderApi'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { orderStore } from '@/store/orderStore'
import CouponCard from '@/components/Price/CouponCard.vue'
import { ServerType } from '@/enums'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const useOrderStore = orderStore()
const {
  vendorCode,
  totalPrice,
  isSelectedCoupon,
  serverType,
  couponId,
  couponType,
  discount,
  couponPrice,
} = storeToRefs(useOrderStore)

const showEmptyIcon = ref(false)
const page = ref(1)
const toSelect = ref(false)
const listTimeOut = ref([])

// 店内码用的是条码制作优惠券
const mixedServerType = computed(() => {
  if (serverType.value === ServerType.storeCode) {
    return ServerType.makeFilm
  } else {
    return serverType.value
  }
})

/**
 * 禁止选择优惠券，返回true代表禁选
 * 支付时筛选优惠券时，筛选厂商识别代码，不满足时灰色
 * @param data
 * @param openDisabled 是否开启可选判断
 */
const canSelect = (data: any, openDisabled: boolean) => {
  // 是否开启禁止判断
  // if (!openDisabled) return false
  if (openDisabled) {
    // 是否过期
    if (data.isTimeOut === 1) return false
    // 服务类型是否相同
    if (data.serverType !== mixedServerType.value) return false
    // 厂商识别码是否符合，服务类型，1：条码制作，2：信息上报
    if (
      mixedServerType.value === ServerType.makeFilm &&
      data.vendorCode !== null &&
      data.vendorCode !== vendorCode.value
    )
      return false
    // 券最低使用价格
    if (data.minUsagePrice !== null && totalPrice.value < data.minUsagePrice) return false
    return true
  }
  return false
}

onLoad((option: any) => {
  // 开启选择模式
  toSelect.value = option.toSelect === 'true'
  run()
  init()
})

const {
  loading,
  error,
  data: list,
  run,
} = useRequest(() =>
  couponPageApi({
    isUsed: 0,
    isTimeOut: 0,
    groupBy: '',
    needTotalCount: true,
    orderBy: 'grantDate',
    orderDirection: OrderDirection.desc,
    pageIndex: 1,
    pageSize: 100000,
    userId: userId.value,
  }),
)

const {
  loading: loadingTimeOut,
  error: errorTimeOut,
  data: dataTimeOut,
  run: runTimeOut,
} = useRequest(() =>
  couponPageApi({
    isUsed: 0,
    isTimeOut: 1,
    groupBy: '',
    needTotalCount: true,
    orderBy: 'grantDate',
    orderDirection: OrderDirection.desc,
    pageIndex: page.value,
    pageSize: 10,
    userId: userId.value,
  }),
)

const init = () =>
  new Promise((resolve, reject) => {
    listTimeOut.value = []
    runTimeOut()
      .then(() => {
        listTimeOut.value = [...dataTimeOut.value]
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })

onPullDownRefresh(() => {
  showEmptyIcon.value = false
  page.value = 1
  init().finally(() => {
    uni.stopPullDownRefresh()
  })
})

// 滚到页面底部加载更多，只针对过期券
onReachBottom(() => {
  if (!showEmptyIcon.value) {
    page.value++
    runTimeOut().then(() => {
      listTimeOut.value = [...listTimeOut.value, ...dataTimeOut.value]
      if (dataTimeOut.value.length === 0) {
        showEmptyIcon.value = true
      }
    })
  }
})

const handleSelect = (data: any) => {
  if (canSelect(data, toSelect.value)) {
    couponId.value = data.couponId
    couponType.value = data.couponType
    discount.value = data.discount
    couponPrice.value = data.couponPrice
    isSelectedCoupon.value = true
    uni.navigateBack()
  }
}
</script>
<template>
  <view class="px-3 pt-3 pb-10">
    <view v-if="toSelect" class="py-3">请选择优惠券：</view>
    <view
      v-for="item in list"
      :key="item.couponId"
      :class="!toSelect || canSelect(item, toSelect) ? 'bg-white' : 'o-bg-white-disable'"
      class="mb-3 rd-2 p-4 relative overflow-hidden"
      @click="handleSelect(item)"
    >
      <coupon-card :data="item" :disabled="toSelect && !canSelect(item, toSelect)" />
    </view>
    <up-empty
      v-if="list?.length === 0"
      icon="https://wx.gs1helper.com/images/common/content.png"
      text="暂无优惠券"
    ></up-empty>
    <view v-if="listTimeOut?.length > 0">
      <view class="py-3">已过期优惠券：</view>
      <view
        v-for="item in listTimeOut"
        :key="item.couponId"
        class="mb-3 rd-2 p-4 relative overflow-hidden bg-white"
      >
        <coupon-card :data="item" :disabled="!canSelect(item, toSelect)" />
      </view>
    </view>
    <view v-if="showEmptyIcon" class="w-full mt-4 o-color-aid text-center text-xs">
      - 已经到底了 -
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
