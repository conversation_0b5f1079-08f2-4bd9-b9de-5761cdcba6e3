<script setup lang="ts">
import { msgModalStore } from '@/store/msgModalStore'

const useMsgModalStore = msgModalStore()
</script>

<template>
  <up-modal
    :show="useMsgModalStore.isGlobalModalShow"
    :title="useMsgModalStore.title"
    :content="useMsgModalStore.content"
    :confirmText="useMsgModalStore.confirmText"
    :showCancelButton="useMsgModalStore.showCancelButton"
    :closeOnClickOverlay="useMsgModalStore.closeOnClickOverlay"
    @confirm="useMsgModalStore.handleConfirm"
    @cancel="useMsgModalStore.isGlobalModalShow = false"
    @close="useMsgModalStore.isGlobalModalShow = false"
  ></up-modal>
</template>

<style scoped lang="scss"></style>
