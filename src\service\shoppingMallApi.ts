import type { FromPlatform, GoodsStatus, ServerType, SpecType } from '@/enums'
import { http } from '@/utils/http'

// 参数接口
export interface MallGoodsPageParams {
  goodsName?: string
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirectionType
  pageIndex?: number
  pageSize?: number
}

// 响应接口
export interface MallGoodsPageRes {
  data: {
    classifyName: string
    goodsClassifyId: number
    goodsId: number
    goodsName: string
    goodsStatus: GoodsStatus
    goodsStatusName: string
    imageUrl: string
    remark: string
    sliderImages: string
    sortNum: number
    specType: SpecType
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 商城商品分页
 * @param {object} params qry
 * @param {string} params.goodsName 商品名称
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @returns
 */
export function mallGoodsPageApi(params: MallGoodsPageParams) {
  return http.post<MallGoodsPageRes>('/api/mallGoodsPage', params)
}

export interface SkuListData {
  attrDetailValueId: number
  goodsImage: string
  originalPrice: number
  remark: string
  specValueNameStr: string
  stock: number
}

// 响应接口
export interface MallGoodsLoadRes {
  data: {
    classifyName: string
    goodsClassifyId: number
    goodsContent: string
    goodsId: number
    goodsName: string
    goodsStatus: number
    goodsStatusName: string
    imageUrl: string
    remark: string
    skuList: SkuListData[]
    sliderImages: string
    sortNum: number
    specList: {
      specId: number
      specName: string
      specValue: string[]
    }[]
    specType: number
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 加载商城商品
 * @param {string} goodsId 商品id
 * @returns
 */
export function mallGoodsLoadApi(goodsId: number) {
  return http.post<MallGoodsLoadRes>(`/api/mallGoodsLoad?goodsId=${goodsId}`)
}

// 参数接口
export interface OrderCreateParams {
  attrDetailValueId: number
  fromTo?: FromPlatform
  goodsId: number
  number: number
  serverType: ServerType
  userId: number
}
// 响应接口
export interface OrderCreateRes {
  data: {
    orderCode: string
    orderId: number
    price: number
    serverType: ServerType
    specValueNameStr: string
    total: number
    totalPrice: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 下单-创建订单
 * @param {object} params cmd
 * @param {number} params.attrDetailValueId 商品skuid
 * @param {number} params.fromTo 来源，1：微信小程序、2：抖音小程序、3：其他平台
 * @param {number} params.goodsId 商品id
 * @param {number} params.number 商品总数量
 * @param {number} params.serverType 服务类型：1：条码制作，2：信息上报,3:包装设计，4、标签印刷，5：店内码，6：代办业务；7：标签模版；8：打印机,9：进口商品报备；，一定要传
 * @param {number} params.userId 用户id
 * @returns
 */
export function orderCreateApi(params: OrderCreateParams) {
  return http.post<OrderCreateRes>('/api/orderCreate', params)
}

// 参数接口
export interface PayMallOrderParams {
  fromTo?: FromPlatform
  orderCode: string
}

// 响应接口
export interface PayMallOrderRes {
  data: {
    currencyType: string
    fee: number
    isNeedToPay: boolean
    nonceStr: string
    packageValue: string
    paySign: string
    paymentArgs: Record<string, unknown>
    signType: string
    timeStamp: string
    version: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 支付，回传前端支付所需参数
 * @param {object} params cmd
 * @param {string} params.orderCode 订单编号
 * @returns
 */
export function payMallOrderApi(params: PayMallOrderParams) {
  return http.post<PayMallOrderRes>('/api/payMallOrder', params)
}

// 参数接口
export interface SumbitOrderParams {
  addressDetail?: string
  contact?: string
  contactPhone?: string
  couponId?: number
  district?: string
  email?: string
  orderCode: string
}

// 响应接口
export interface SumbitOrderRes {
  data: {
    actuallyPrice: number
    discountsPrice: number
    orderCode: string
    orderId: number
    price: number
    serverType: number
    specValueNameStr: string
    total: number
    totalPrice: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 提交订单
 * @param {object} params cmd
 * @param {string} params.addressDetail 收货人详细地址
 * @param {string} params.contact 联系人，收货人真实姓名
 * @param {string} params.contactPhone 联系电话,收货人电话
 * @param {number} params.couponId 优惠券id
 * @param {string} params.district 收货人所在区
 * @param {string} params.email 邮箱,标签模版需要填写
 * @param {string} params.orderCode 订单Code
 * @returns
 */
export function sumbitMallOrder(params: SumbitOrderParams) {
  return http.post<SumbitOrderRes>('/api/sumbitMallOrder', params)
}

// 参数接口
export interface ExpressOrderPageParams {
  groupBy?: string
  isInvoiced?: number
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: string
  pageIndex?: number
  pageSize?: number
  payState?: number
  serverType?: number
  userId: number
}

// 响应接口
export interface ExpressOrderPageRes {
  data: {
    actuallyPrice: number
    addressDetail: string
    contact: string
    contactPhone: string
    createdDate: Record<string, unknown>
    district: string
    invoiceId: number
    invoiceState: number
    invoiceStateStr: string
    isHasOtherServer: boolean
    number: number
    orderCode: string
    orderContent: string
    orderId: number
    payDate: Record<string, unknown>
    payState: number
    payStateStr: string
    price: number
    priceUnit: string
    serverType: number
    totalPrice: number
    transactionNo: string
    userId: number
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 所有物流的订单的分页
 * @param {object} params qry
 * @param {string} params.groupBy
 * @param {number} params.isInvoiced 发票状态，1：已开票，0：未开票
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.payState 支付状态，-1:新建；0：未支付，1：已支付（支付成功），2：支付失败，
 * @param {number} params.serverType 服务类型：1：条码制作，2：信息上报,3:包装设计，4、标签印刷，5：店内码，6：续展；10：注册；12：变更；7：标签设计；8：打印机；9：进口商品报备；11：微站订单
 * @param {number} params.userId 用户id
 * @returns
 */
export function expressOrderPageApi(params: ExpressOrderPageParams) {
  return http.post<ExpressOrderPageRes>('/api/expressOrderPage', params)
}
