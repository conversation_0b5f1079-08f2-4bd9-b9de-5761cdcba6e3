<script setup lang="ts">
import { jumpToWeChatCustomerService } from '@/hooks/useCustomerService'
</script>

<template>
  <!--  #ifndef MP-TOUTIAO -->
  <button
    class="f-customer-service o-btn-no-style absolute"
    @click="jumpToWeChatCustomerService"
  />
  <!--  #endif -->
  <!--  #ifdef MP-TOUTIAO -->
  <button
    class="f-customer-service o-btn-no-style absolute"
    open-type="im"
    data-im-id="77222292537"
  />
  <!--  #endif -->
</template>

<style scoped lang="scss">
.f-customer-service {
  $w: 100rpx;
  position: fixed;
  right: 40rpx;
  bottom: calc(260rpx + env(safe-area-inset-bottom));
  width: $w;
  height: $w;
  background-image: url('https://wx.gs1helper.com/images/p_index_customer_service.png');
  background-size: cover;
  border-radius: calc($w / 2);
  box-shadow: 0 21px 11.4px -15px rgba(22, 93, 255, 0.56);
  overflow: inherit;

  &:after {
    @apply: color-gray text-center absolute;

    content: '客服';
    font-size: 5vw;
    top: 95rpx;
    left: 0;
  }
}
</style>
