<script setup lang="ts">
import { jumpToWeChatCustomerService } from '@/hooks/useCustomerService'

withDefaults(
  defineProps<{
    content?: string
  }>(),
  {
    content: '遇到问题？咨询下客服',
  },
)
</script>

<template>
  <!--  #ifndef MP-TOUTIAO-->
  <button class="o-customer-service-long text-sm p-3 mt-3" @click="jumpToWeChatCustomerService">
    <image
      class="o-cs-img mr-1"
      src="https://wx.gs1helper.com/images/p_index_customer_service.png"
    />
    {{ content }}
  </button>
  <!--  #endif-->
  <!--  #ifdef MP-TOUTIAO-->
  <button class="o-customer-service-long text-sm p-3 mt-3" open-type="im" data-im-id="77222292537">
    <image
      class="o-cs-img mr-1"
      src="https://wx.gs1helper.com/images/p_index_customer_service.png"
    />
    {{ content }}
  </button>
  <!--  #endif -->
</template>

<style scoped lang="scss"></style>
