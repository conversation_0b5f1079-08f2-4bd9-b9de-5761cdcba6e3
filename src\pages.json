{"globalStyle": {"navigationStyle": "default", "navigationBarTitleText": "barcode_center_wx", "navigationBarBackgroundColor": "#f8f8f8", "navigationBarTextStyle": "black", "backgroundColor": "#FFFFFF"}, "easycom": {"autoscan": true, "custom": {"^fg-(.*)": "@/components/fg-$1/fg-$1.vue", "^u--(.*)": "uview-plus/components/u-$1/u-$1.vue", "^up-(.*)": "uview-plus/components/u-$1/u-$1.vue", "^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"}}, "tabBar": {"color": "#999999", "selectedColor": "#165dff", "backgroundColor": "#F8F8F8", "borderStyle": "white", "height": "1px", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "list": [{"iconPath": "/static/tabbar/home.png", "selectedIconPath": "/static/tabbar/homeH.png", "pagePath": "pages/index/index", "text": "", "iconType": "uiLib", "icon": ""}, {"iconPath": "/static/tabbar/code.png", "selectedIconPath": "/static/tabbar/codeH.png", "pagePath": "pages/myFilm/index", "text": "", "iconType": "uiLib", "icon": ""}, {"iconPath": "/static/tabbar/product.png", "selectedIconPath": "/static/tabbar/productH.png", "pagePath": "pages/infoReportMg/index", "text": "", "iconType": "uiLib", "icon": ""}, {"iconPath": "/static/tabbar/user.png", "selectedIconPath": "/static/tabbar/userH.png", "pagePath": "pages/userPage/index", "text": "", "iconType": "uiLib", "icon": ""}]}, "condition": {"current": 1, "list": [{"name": "indexWithQuery", "path": "pages/index/index", "query": "scene=emailop%3DXGxvB"}, {"name": "invoiceInfo", "path": "pages/myOrder/invoiceInfo", "query": ""}, {"name": "myOrder", "path": "pages/myOrder/index", "query": ""}, {"name": "myFilm", "path": "pages/myFilm/index", "query": ""}, {"name": "userPage", "path": "pages/userPage/index", "query": ""}, {"name": "orderConfirm", "path": "pages/orderConfirm/makeFilm", "query": ""}, {"name": "infoReportSuccess", "path": "pages/paymentSuccess/infoReport", "query": ""}, {"name": "agencyServiceSuccess", "path": "pages/paymentSuccess/agencyService", "query": ""}, {"name": "miniShopSuccess", "path": "pages/paymentSuccess/miniShop", "query": ""}, {"name": "defaultSuccess", "path": "pages/paymentSuccess/default", "query": ""}, {"name": "infoReport", "path": "pages/infoReport/index", "query": ""}, {"name": "order_infoReport", "path": "pages/orderConfirm/infoReport", "query": ""}, {"name": "myInfoReport", "path": "pages/infoReport/myInfoReport", "query": ""}, {"name": "my<PERSON><PERSON><PERSON>", "path": "pages/myAgency/index", "query": ""}, {"name": "<PERSON><PERSON><PERSON>wal", "path": "pages/myAgency/submitRenewal", "query": ""}]}, "pages": [{"path": "pages/index/index", "type": "home", "layout": "tabbar", "style": {"navigationBarTitleText": "商品标准条码制作", "navigationStyle": "custom"}}, {"path": "pages/about/filmSpecification", "type": "page", "style": {"navigationBarTitleText": "条码使用说明"}}, {"path": "pages/about/index", "type": "page", "style": {"navigationBarTitleText": "关于我们"}}, {"path": "pages/about/statement", "type": "page", "style": {"navigationBarTitleText": "平台免责声明"}}, {"path": "pages/addressPage/editor", "type": "page", "style": {"navigationBarTitleText": "收货地址编辑"}}, {"path": "pages/addressPage/index", "type": "page", "style": {"navigationBarTitleText": "收货地址", "enablePullDownRefresh": true, "backgroundColor": "#f0f3f8"}}, {"path": "pages/agencyService/index", "type": "page", "style": {"navigationBarTitleText": "注册续展变更办理"}}, {"path": "pages/agencyService/modifyService", "type": "page", "style": {"navigationBarTitleText": "条码成员信息变更"}}, {"path": "pages/agencyService/registerService", "type": "page", "style": {"navigationBarTitleText": "条码注册申请"}}, {"path": "pages/agencyService/renewalService", "type": "page", "style": {"navigationBarTitleText": "条码续约续展"}}, {"path": "pages/consultingService/index", "type": "page", "style": {"navigationBarTitleText": "服务页面"}}, {"path": "pages/designServer/index", "type": "page", "style": {"navigationBarTitleText": "包装设计"}}, {"path": "pages/discountCoupon/index", "type": "page", "style": {"navigationBarTitleText": "红包卡券", "enablePullDownRefresh": true, "backgroundColor": "#f0f3f8"}}, {"path": "pages/importedGoods/index", "type": "page", "style": {"navigationBarTitleText": "进口商品报备"}}, {"path": "pages/infoReport/index", "type": "page", "style": {"navigationBarTitleText": "产品编码信息通报"}}, {"path": "pages/infoReport/myInfoReport", "type": "page", "style": {"navigationBarTitleText": "我的信息通报"}}, {"path": "pages/infoReportMg/index", "type": "page", "layout": "tabbar", "style": {"navigationBarTitleText": "商品通报管理"}}, {"path": "pages/invoiceTemple/editor", "type": "page", "style": {"navigationBarTitleText": "开票信息编辑"}}, {"path": "pages/invoiceTemple/index", "type": "page", "style": {"navigationBarTitleText": "开票信息", "enablePullDownRefresh": true, "backgroundColor": "#f0f3f8"}}, {"path": "pages/labelPrint/index", "type": "page", "style": {"navigationBarTitleText": "标签印刷"}}, {"path": "pages/makeFilm/index", "type": "page", "style": {"navigationBarTitleText": "条码制作"}}, {"path": "pages/makeFilm/vendorCodePage", "type": "page", "style": {"navigationBarTitleText": "查询厂商识别代码"}}, {"path": "pages/miniShop/index", "type": "page", "style": {"navigationBarTitleText": "二维码微站"}}, {"path": "pages/miniShop/viewWebPage", "type": "page"}, {"path": "pages/myAgency/index", "type": "page", "style": {"navigationBarTitleText": "我的业务"}}, {"path": "pages/myAgency/submitModify", "type": "page", "style": {"navigationBarTitleText": "变更业务办理"}}, {"path": "pages/myAgency/submitRegister", "type": "page", "style": {"navigationBarTitleText": "注册业务办理"}}, {"path": "pages/myAgency/submitRenewal", "type": "page", "style": {"navigationBarTitleText": "续展业务办理"}}, {"path": "pages/myFilm/index", "type": "page", "layout": "tabbar", "style": {"navigationBarTitleText": "我的条码"}}, {"path": "pages/myOrder/index", "type": "page", "style": {"navigationBarTitleText": "订单开票", "enablePullDownRefresh": true, "backgroundColor": "#f0f3f8"}}, {"path": "pages/myOrder/invoiceInfo", "type": "page", "style": {"navigationBarTitleText": "开票资料"}}, {"path": "pages/myOrder/logisticsDetail", "type": "page", "style": {"navigationBarTitleText": "物流详情"}}, {"path": "pages/myOrder/logisticsOrder", "type": "page", "style": {"navigationBarTitleText": "物流订单", "enablePullDownRefresh": true, "backgroundColor": "#f0f3f8"}}, {"path": "pages/orderConfirm/default", "type": "page", "style": {"navigationBarTitleText": "订单确认"}}, {"path": "pages/orderConfirm/infoReport", "type": "page", "style": {"navigationBarTitleText": "订单确认"}}, {"path": "pages/orderConfirm/labelPrint", "type": "page", "style": {"navigationBarTitleText": "订单确认"}}, {"path": "pages/orderConfirm/makeFilm", "type": "page", "style": {"navigationBarTitleText": "订单确认"}}, {"path": "pages/orderConfirm/miniShop", "type": "page", "style": {"navigationBarTitleText": "订单确认"}}, {"path": "pages/orderConfirm/shoppingMall", "type": "page", "style": {"navigationBarTitleText": "订单确认"}}, {"path": "pages/orderConfirm/storeCode", "type": "page", "style": {"navigationBarTitleText": "订单确认"}}, {"path": "pages/paymentSuccess/agencyService", "type": "page", "style": {"navigationBarTitleText": "支付成功"}}, {"path": "pages/paymentSuccess/default", "type": "page", "style": {"navigationBarTitleText": "支付成功"}}, {"path": "pages/paymentSuccess/designServer", "type": "page", "style": {"navigationBarTitleText": "支付成功"}}, {"path": "pages/paymentSuccess/infoReport", "type": "page", "style": {"navigationBarTitleText": "支付成功"}}, {"path": "pages/paymentSuccess/makeFilm", "type": "page", "style": {"navigationBarTitleText": "支付成功"}}, {"path": "pages/paymentSuccess/miniShop", "type": "page", "style": {"navigationBarTitleText": "支付成功"}}, {"path": "pages/shoppingMall/index", "type": "page", "style": {"navigationBarTitleText": "业务", "navigationStyle": "custom"}}, {"path": "pages/storeCode/index", "type": "page", "style": {"navigationBarTitleText": "店内条码制作"}}, {"path": "pages/tutorials/contextPage", "type": "page", "style": {"navigationBarTitleText": "文章内容"}}, {"path": "pages/tutorials/index", "type": "page", "style": {"navigationBarTitleText": "规范学习"}}, {"path": "pages/tutorials/searchPage", "type": "page", "style": {"navigationBarTitleText": "搜索"}}, {"path": "pages/tutorials/violation", "type": "page", "style": {"navigationBarTitleText": "避免违规"}}, {"path": "pages/userPage/index", "type": "page", "layout": "tabbar", "style": {"navigationBarTitleText": "用户信息", "navigationStyle": "custom"}}, {"path": "pages/userPage/myInformation", "type": "page", "style": {"navigationBarTitleText": "我的信息"}}], "subPackages": []}