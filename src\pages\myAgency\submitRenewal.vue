<route lang="json5">
{
  style: {
    navigationBarTitleText: '续展业务办理',
  },
}
</route>
<script setup lang="ts">
import { submitServiceStore } from '@/store/submitServiceStore'
import { storeToRefs } from 'pinia'
import {
  orderRenewalCreateApi,
  OrderRenewalCreateParams,
  UploadImageRes,
  uploadOrderOtherImageApiPath,
} from '@/service/agencyServiceApi'
import { validatePhoneNumber } from '@/utils/tool'
import CsLongButton from '@/components/customer/csLongButton.vue'
import { useUserStore } from '@/store/user'
import { BASE64IMG_PREFIX, UPLOAD_IMG_MAXSIZE } from '@/enums'

const userStore = useUserStore()
const useSubmitServiceStore = submitServiceStore()
const { orderInfoData } = storeToRefs(useSubmitServiceStore)
// 图片最大3M
const BASEURL = import.meta.env.VITE_SERVER_BASEURL
const isShowPassword = ref(true)
const isShowModal = ref(false)
const loading = ref(false)
const form = ref(null)
const companyLicenseFiles = ref([])
const companyLicenseChangeFiles = ref([])
const payCetificateFiles = ref([])

// 符合单向数据流
const model = reactive<OrderRenewalCreateParams>({
  barCodeCardNum: orderInfoData.value.barCodeCardNum ?? '',
  barCodeCardPassword: orderInfoData.value.barCodeCardPassword ?? '',
  companyLicenseChangeUrl: orderInfoData.value.companyLicenseChangeUrl ?? '',
  companyLicenseUrl: orderInfoData.value.companyLicenseUrl ?? '',
  contact: orderInfoData.value.contact ?? '',
  contactPhone: orderInfoData.value.contactPhone ?? '',
  email: orderInfoData.value.email ?? '',
  isHasChange: orderInfoData.value.isHasChange ?? false,
  orderId: orderInfoData.value.orderId ?? null,
  payCetificate: orderInfoData.value.payCetificate ?? '',
})

const rules = {
  contact: [{ required: true, message: '请输入联系人姓名', trigger: ['blur'] }],
  contactPhone: [
    {
      required: true,
      message: '请输入联系人手机',
      trigger: ['blur'],
    },
    {
      validator: validatePhoneNumber,
      message: '请输入正确的手机号',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  email: [
    {
      required: true,
      message: '请输入电子邮箱地址',
      trigger: ['blur'],
    },
    {
      validator: (rule, value, callback) => {
        return uni.$u.test.email(value)
      },
      message: '请输入正确的邮箱地址',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  barCodeCardNum: [{ required: true, message: '忘记？填注册所留联系手机', trigger: ['blur'] }],
}

// 删除图片
const deletePayCetificate = (event) => {
  payCetificateFiles.value.splice(event.index, 1)
  model.payCetificate = ''
}
const deleteCompanyLicense = (event) => {
  companyLicenseFiles.value.splice(event.index, 1)
  model.companyLicenseUrl = ''
}
const deleteCompanyLicenseChange = (event) => {
  companyLicenseChangeFiles.value.splice(event.index, 1)
  model.companyLicenseChangeUrl = ''
}

const payCetificateAfterRead = (event) => {
  handleFileUpload(event, payCetificateFiles, 'payCetificate')
}

const companyLicenseUrlAfterRead = (event) => {
  handleFileUpload(event, companyLicenseFiles, 'companyLicenseUrl')
}

const companyLicenseChangeAfterRead = (event) => {
  handleFileUpload(event, companyLicenseChangeFiles, 'companyLicenseChangeUrl')
}

const handleFileUpload = async (
  event,
  filesRef,
  modelProperty: 'companyLicenseChangeUrl' | 'companyLicenseUrl' | 'payCetificate',
) => {
  const lists = [].concat(event.file)
  let fileListLen = filesRef.value.length

  // 添加上传状态
  lists.forEach((item) => {
    filesRef.value.push({ ...item, status: 'uploading', message: '上传中' })
  })

  // 逐个上传并更新状态
  for (let i = 0; i < lists.length; i++) {
    const result = await uploadFilePromise(lists[i].url)
    const item = filesRef.value[fileListLen]
    // fileRef 的结构是up-upload的结构，不要变，只能加
    filesRef.value.splice(fileListLen, 1, {
      ...item,
      status: 'success',
      message: '',
      url: BASE64IMG_PREFIX + result.image,
      realUrl: result.url,
    })
    fileListLen++
  }

  // 更新模型属性
  model[modelProperty] = filesRef.value[0]?.realUrl || ''
}

const uploadFilePromise = (url) => {
  return new Promise<UploadImageRes['data']>((resolve, reject) => {
    const a = uni.uploadFile({
      url: BASEURL + uploadOrderOtherImageApiPath, // 仅为示例，非真实的接口地址
      filePath: url,
      name: 'file',
      header: {
        Authorization: userStore.authorization,
      },
      success: (res) => {
        console.log(res)
        const d = JSON.parse(res.data) as UploadImageRes
        if (d.success) {
          resolve(d.data)
        } else {
          reject(d.errMessage)
        }
      },
      fail: (err) => {
        console.log(err)
        reject(err)
      },
    })
  })
}

const handleSubmit = () => {
  if (loading.value) return
  form.value
    .validate()
    .then((valid) => {
      if (valid) {
        if (!model.payCetificate) {
          uni.showToast({
            title: '请上传付款凭证',
            icon: 'none',
            duration: 2000,
          })
          return
        }
        if (!model.companyLicenseUrl) {
          uni.showToast({
            title: '请上传营业执执照复印件',
            icon: 'none',
            duration: 2000,
          })
          return
        }
        if (model.isHasChange && !model.companyLicenseChangeUrl) {
          uni.showToast({
            title: '请上传变更后的营业执照复印件',
            icon: 'none',
            duration: 2000,
          })
          return
        }
        loading.value = true
        orderRenewalCreateApi({
          barCodeCardNum: model.barCodeCardNum,
          barCodeCardPassword: model.barCodeCardPassword,
          companyLicenseChangeUrl: model.companyLicenseChangeUrl,
          companyLicenseUrl: model.companyLicenseUrl,
          contact: model.contact,
          contactPhone: model.contactPhone,
          email: model.email,
          isHasChange: model.isHasChange,
          orderId: model.orderId,
          payCetificate: model.payCetificate,
        })
          .then(() => {
            isShowModal.value = true
          })
          .finally(() => {
            loading.value = false
          })
      }
    })
    .catch((err) => {
      console.log(err)
      uni.pageScrollTo({
        selector: '#fTop',
      })
      const title = err[0]?.message ?? '请输入必填项'
      uni.showToast({
        title,
        icon: 'none',
      })
    })
}

onMounted(() => {
  if (orderInfoData.value.companyLicenseUrl) {
    companyLicenseFiles.value = [
      {
        status: 'success',
        message: '',
        url: BASE64IMG_PREFIX + orderInfoData.value.companyLicenseImage,
      },
    ]
  }
  if (orderInfoData.value.companyLicenseChangeUrl) {
    companyLicenseChangeFiles.value = [
      {
        status: 'success',
        message: '',
        url: BASE64IMG_PREFIX + orderInfoData.value.companyLicenseChangeImage,
      },
    ]
  }
  if (orderInfoData.value.payCetificate) {
    payCetificateFiles.value = [
      {
        status: 'success',
        message: '',
        url: BASE64IMG_PREFIX + orderInfoData.value.payCetificateImage,
      },
    ]
  }
})

const handleOk = () => {
  isShowModal.value = false
  uni.navigateBack()
}
</script>

<template>
  <view class="p-4">
    <up-form labelWidth="80" ref="form" :model="model" :rules="rules">
      <view id="fTop" class="bg-white rd-md py-4 pl-6 pr-4">
        <up-form-item label="联系人" prop="contact" required>
          <up-input border="bottom" v-model="model.contact" placeholder="联系人姓名" clearable />
        </up-form-item>
        <up-form-item label="联系手机" prop="contactPhone" required>
          <up-input
            border="bottom"
            type="number"
            :maxlength="11"
            clearable
            v-model="model.contactPhone"
            placeholder="联系手机"
          >
            <template #suffix>
              <text class="color-gray text-xs">{{ model.contactPhone.length }}/11</text>
            </template>
          </up-input>
        </up-form-item>
        <up-form-item label="电子邮箱" prop="email" required>
          <up-input
            v-model="model.email"
            clearable
            border="bottom"
            placeholder="用于接收办理结果通知"
          ></up-input>
        </up-form-item>
      </view>
      <view class="bg-white rd-md p-4 mt-2">
        <up-form-item label="条码卡号" prop="barCodeCardNum">
          <up-input
            border="bottom"
            clearable
            v-model="model.barCodeCardNum"
            placeholder="忘记？填所注册留联系手机"
          ></up-input>
        </up-form-item>
        <view class="flex gap-1 items-center">
          <up-form-item label="条码卡密码" prop="barCodeCardPassword">
            <up-input
              border="bottom"
              :password="isShowPassword"
              clearable
              v-model="model.barCodeCardPassword"
              placeholder=""
            ></up-input>
          </up-form-item>
          <view class="o-color-aid text-sm shrink-0" @click="isShowPassword = !isShowPassword">
            {{ isShowPassword ? '隐藏' : '显示' }}
          </view>
        </view>
        <view class="text-xs text-right o-color-aid">
          <text class="color-red pr-1">*</text>
          若忘记密码，可让客服通过手机验证码重置
        </view>
      </view>
    </up-form>
    <view class="mt-3 text-sm o-color-aid o-p">
      如未正确提供条码卡号及密码，请留意接听客服电话，或添加客服为好友，才可接收客服信息。
    </view>
    <cs-long-button content="添加您的专属客服" />
    <view class="bg-white rd-md p-4 mt-2">
      <view class="mt-4 mb-2">
        <text class="color-red pr-1 shrink-0">*</text>
        <text>系统维护费汇款证明复印件或者扫描件：</text>
      </view>
      <up-upload
        class="pl-6"
        :fileList="payCetificateFiles"
        @afterRead="payCetificateAfterRead"
        @delete="deletePayCetificate"
        name="1"
        :multiple="false"
        :maxCount="1"
        :maxSize="UPLOAD_IMG_MAXSIZE"
      ></up-upload>
      <view class="mt-4 mb-2 flex">
        <text class="color-red pr-1 shrink-0">*</text>
        <text>营业执执照复印件加盖公章后的图片：</text>
      </view>
      <up-upload
        class="pl-6"
        :fileList="companyLicenseFiles"
        @afterRead="companyLicenseUrlAfterRead"
        @delete="deleteCompanyLicense"
        name="1"
        :multiple="false"
        :maxCount="1"
        :maxSize="UPLOAD_IMG_MAXSIZE"
      ></up-upload>
      <template v-if="model.isHasChange">
        <view class="mt-4 mb-2 flex">
          <text class="color-red pr-1 shrink-0">*</text>
          <text>营业执照变更证明（市场监管部门出具的）复印件加盖公章扫描件：</text>
        </view>
        <up-upload
          class="pl-6"
          :fileList="companyLicenseChangeFiles"
          @afterRead="companyLicenseChangeAfterRead"
          @delete="deleteCompanyLicenseChange"
          name="1"
          :multiple="false"
          :maxCount="1"
          :maxSize="UPLOAD_IMG_MAXSIZE"
        ></up-upload>
      </template>
    </view>
    <view
      class="p-3 mt-4 flex-grow-1 flex items-center justify-center color-white font-bold rd-2"
      :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
      @click="handleSubmit"
    >
      提交资料
    </view>
    <view class="p-8"></view>
    <up-modal
      :show="isShowModal"
      title="资料提交成功"
      content="如未正确提供条码卡号及密码，请留意接听客服电话，或添加客服为好友。"
      @confirm="handleOk"
    ></up-modal>
  </view>
</template>

<style scoped lang="scss"></style>
