<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '查询厂商识别代码',
  },
}
</route>
<script lang="ts" setup>
import { getCompanyVendorCodeApi, GetCompanyVendorCodeResData } from '@/service/barcodePageApi'
import { vendorCodeStore } from '@/store/vendorCodeStore'
import { storeToRefs } from 'pinia'
import { jumpToWeChatCustomerService } from '@/hooks/useCustomerService'
import debounce from 'debounce'

const useVendorCodeStore = vendorCodeStore()
const { companyName, vendorCode } = storeToRefs(useVendorCodeStore)

const companyNameInput = ref('')
const showEmptyIcon = ref(false)
const list = ref<any[]>([])

// 将字符串所有中文"（"和"）"转成英文"("和")"
const toEnglish = (str: string) => {
  return str.replace(/（/g, '(').replace(/）/g, ')')
}

const getData = () => {
  showEmptyIcon.value = false
  list.value = []
  uni.showLoading({
    title: '加载中',
  })
  getCompanyVendorCodeApi({
    companyName: toEnglish(companyNameInput.value),
  })
    .then((res) => {
      if (res.data.length > 0) {
        list.value = res.data
      } else {
        debounce(() => {
          showEmptyIcon.value = true
        }, 2000)
      }
    })
    .catch(() => {
      showEmptyIcon.value = true
    })
    .finally(() => {
      uni.hideLoading()
    })
}

const handleSearch = () => {
  // companyNameInput至少6个字
  if (companyNameInput.value.length >= 2) {
    getData()
  } else {
    uni.showToast({
      title: '请输入至少2个字',
      icon: 'none',
    })
  }
}

const handleSelect = (data: GetCompanyVendorCodeResData) => {
  companyName.value = data.companyName
  vendorCode.value = data.vendorCode
  uni.navigateBack()
}

const handleInput = debounce(() => {
  handleSearch()
}, 600)
</script>
<template>
  <view class="sticky w-full top-0 left-0 z-1">
    <view class="py-2 px-4 bg-white">
      <view class="o-bg-no flex items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-input
          v-model="companyNameInput"
          :maxlength="50"
          border="none"
          class="grow o-bg-transparent"
          clearable
          placeholder="请输入公司名称"
          @change="handleInput"
        ></up-input>
        <up-icon name="search" size="20" @click="handleSearch"></up-icon>
      </view>
    </view>
  </view>
  <up-alert
    v-if="list.length > 0"
    type="warning"
    description="点击即可回填厂商识别码"
    :showIcon="true"
    closable
  ></up-alert>
  <view class="pt-1 pb-6 flex flex-col">
    <view
      v-for="item in list"
      :key="item.vendorCode"
      class="f-item flex justify-between text-sm px-3 py-4"
      @click="handleSelect(item)"
    >
      <view>{{ item.companyName }}</view>
      <view class="color-gray">{{ item.vendorCode }}</view>
    </view>
    <view v-if="companyNameInput" class="mt-20 px-6 o-color-aid center flex-col text-sm">
      <text class="text-center">未查到数据？公司名是否正确？</text>
      <text class="text-center">数据存在滞后，如已知厂商识别代码，</text>
      <text class="text-center">可直接返回输入</text>
      <text class="mt-6 text-center">紧急可先联系客服获取</text>
      <button
        class="f-customer-service px-3 text-sm pt-3 pb-2 mt-2"
        @click="jumpToWeChatCustomerService"
      >
        <image
          class="o-cs-img mr-1"
          src="https://wx.gs1helper.com/images/p_index_customer_service.png"
        />
        联系客服
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-item:nth-child(even) {
  background-color: #fff;
}

.f-customer-service {
  width: 40%;
  color: rgba(0, 0, 0, 0.85);
  background-color: #fff;
}
</style>
