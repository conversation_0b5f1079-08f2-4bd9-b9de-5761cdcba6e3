<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '搜索',
  },
}
</route>
<script lang="ts" setup>
import { articlePageApi, ArticlePageRes } from '@/service/tutorialsApi'
import { OrderDirection } from '@/enums/httpEnum'
import ArticleCard from '@/components/tutorials/articleCard.vue'

const articleTitle = ref('')
const page = ref<number>(1)
const showEmptyIcon = ref(false)
const list = ref<any[]>([])

const { loading, error, data, run } = useRequest<ArticlePageRes>(() =>
  articlePageApi({
    articleTitle: articleTitle.value,
    groupBy: '',
    needTotalCount: true,
    orderBy: '',
    orderDirection: OrderDirection.asc,
    pageIndex: page.value,
    pageSize: 20,
  }),
)

const init = () =>
  new Promise((resolve, reject) => {
    list.value = []
    page.value = 1
    showEmptyIcon.value = false
    run()
      .then(() => {
        list.value = [...data.value]
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })

// 滚到页面底部加载更多
onReachBottom(() => {
  if (!showEmptyIcon.value) {
    page.value++
    run().then(() => {
      list.value = [...list.value, ...data.value]
      if (data.value.length === 0) {
        showEmptyIcon.value = true
      }
    })
  }
})

const scrollTop = ref<number>(0)
onPageScroll((e) => {
  scrollTop.value = e.scrollTop
})

const handleSearch = () => {
  init()
}
</script>
<template>
  <view class="sticky w-full top-0 left-0 z-1">
    <view class="py-2 px-4 bg-white">
      <view class="o-bg-no flex items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-input
          v-model="articleTitle"
          border="none"
          class="grow o-bg-transparent"
          placeholder="搜索标题"
          clearable
          :maxlength="50"
        ></up-input>
        <up-icon name="search" size="20" @click="handleSearch"></up-icon>
      </view>
    </view>
  </view>
  <view class="px-3 pt-3 pb-6">
    <article-card v-for="item in list" :key="item.articleId" :data="item" />
    <view class="w-full o-color-aid text-center" v-if="showEmptyIcon">- 已经到底了 -</view>
  </view>
  <up-back-top :scroll-top="scrollTop"></up-back-top>
</template>

<style lang="scss" scoped></style>
