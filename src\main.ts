import '@/style/index.scss'
import 'virtual:uno.css'
import { createSSRApp } from 'vue'
import App from './App.vue'
import { prototypeInterceptor, requestInterceptor } from './interceptors'
import store from './store'
import '@/style/myStyle.scss'
import uviewPlus from 'uview-plus'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  // app.use(routeInterceptor) // 不需要路由守卫
  app.use(requestInterceptor)
  app.use(prototypeInterceptor).use(uviewPlus)
  return {
    app,
  }
}
