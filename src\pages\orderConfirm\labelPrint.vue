<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单确认',
  },
}
</route>

<script lang="ts" setup>
import type {
  SumbitReportOrderParams,
} from '@/service/orderApi'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { douYinRequestPayment, wxRequestPayment } from '@/components/mix'
import PriceBox from '@/components/Price/PriceBox.vue'
import { Article } from '@/enums'
import { addressPageApi } from '@/service/addressPageApi'
import {
  getDouYinOrderPreDataApi,
  payReportOrderApi,
  sumbitReportOrderApi,
} from '@/service/orderApi'
import { msgModalStore } from '@/store/msgModalStore'
import { orderStore } from '@/store/orderStore'
import { useServiceStore } from '@/store/serviceStore'
import { useUserStore } from '@/store/user'
import { catchErrorAndNavigateBack } from '@/utils'

const useMsgModalStore = msgModalStore()

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)
const useOrderStore = orderStore()
const {
  couponId,
  orderCode,
  tempType,
  price,
  total,
  tempName,
  couponPrice,
  discount,
  totalPrice,
  actuallyPrice,
  discountsPrice,
  couponType,
  isSelectedCoupon,
  vendorCode,
} = storeToRefs(useOrderStore)

const btnDisable = ref(false)
const btnStr = ref('提交订单')
const agree = ref(false)
const isModalShow = ref(false)

const addressData = reactive({
  addressDetail: '',
  district: '',
  phone: '',
  realName: '',
})

onLoad(() => {
  // console.log(option)
  useOrderStore.clearOrderInfo()
  useOrderStore.clearCouponInfo()
  getOrderPrice()
})

onShow(() => {
  getAddressData()
})

function getOrderPrice(withAddress: boolean = false) {
  return new Promise((resolve, reject) => {
    btnDisable.value = true
    const params: SumbitReportOrderParams = {
      orderCode: orderCode.value,
    }
    if (withAddress) {
      params.addressDetail = addressData.addressDetail
      params.contact = addressData.realName
      params.contactPhone = addressData.phone
      params.district = addressData.district
    }
    sumbitReportOrderApi(params)
      .then((res: any) => {
        const d = res.data
        actuallyPrice.value = d.actuallyPrice
        price.value = d.price
        total.value = d.total
        totalPrice.value = d.totalPrice
        discountsPrice.value = d.discountsPrice
        tempName.value = d.tempName
        // d.tempType 后期不用了，改用serverType
        tempType.value = d.serverType
        btnDisable.value = false
        resolve(true)
      })
      .catch((err) => {
        btnDisable.value = false
        useOrderStore.clearCouponInfo()
        reject(err)
      })
  })
}

const handleSubmit = debounce(
  () => {
    if (!btnDisable.value) {
      if (addressData.phone) {
        if (agree.value) {
          const url = '/pages/paymentSuccess/default'

          btnDisable.value = true
          uni.showLoading({
            title: '支付中',
          })

          // #ifdef MP-WEIXIN
          // 微信支付逻辑
          getOrderPrice(true).then(() => {
            payReportOrderApi({
              fromTo: import.meta.env.VITE_FROM_PLATFORM,
              orderCode: orderCode.value,
            })
              .then((resData: any) => {
                if (resData.data.isNeedToPay) {
                  wxRequestPayment(resData.data)
                    .then(() => {
                      btnDisable.value = false
                      uni.hideLoading()
                      uni.redirectTo({
                        url,
                      })
                    })
                    .catch(() => {
                      btnDisable.value = false
                      uni.hideLoading()
                    })
                }
                else {
                  btnDisable.value = false
                  uni.hideLoading()
                  uni.redirectTo({
                    url,
                  })
                }
              })
              .catch((err: string) => {
                btnDisable.value = false
                uni.hideLoading()
                catchErrorAndNavigateBack(err)
              })
          })
          // #endif

          // #ifdef MP-TOUTIAO
          // 抖音支付逻辑
          getDouYinOrderPreDataApi({
            orderCode: orderCode.value,
            params: '',
            path: 'pages/index/index',
          }).then((res) => {
            // console.log(res.data)
            if (res.data.isZeroOrder) {
              // 无需支付
              btnDisable.value = false
              uni.hideLoading()
              uni.redirectTo({
                url,
              })
            }
            else {
              const d = {
                orderCode: orderCode.value,
                ...res.data,
              }
              douYinRequestPayment(d)
                .then(() => {
                  btnDisable.value = false
                  uni.hideLoading()
                  uni.redirectTo({
                    url,
                  })
                })
                .catch(() => {
                  btnDisable.value = false
                  uni.hideLoading()
                })
            }
          })
          // #endif
        }
        else {
          useMsgModalStore
            .confirm({
              title: '温馨提示',
              content: '请先勾选已阅读并同意《付款免责声明》',
            })
            .then(() => {
              uni.pageScrollTo({
                selector: '#agreeElement',
              })
            })
        }
      }
      else {
        useMsgModalStore.confirm({
          title: '温馨提示',
          content: '请先添加地址',
        })
      }
    }
  },
  1000,
  { immediate: true },
)

function toDiscountCouponPage() {
  uni.navigateTo({
    url: '/pages/discountCoupon/index?toSelect=true',
  })
}

function getAddressData() {
  addressPageApi({
    // isDefault: false,
    // groupBy: "",
    // needTotalCount: true,
    // orderBy: 'createdDate',
    // orderDirection: OrderDirection.desc,
    pageIndex: 1,
    pageSize: 100000,
    userId: userId.value,
  }).then((res) => {
    if (res.data && res.data.length > 0) {
      const d = res.data.filter(item => item.isDefault)
      if (d.length > 0) {
        addressData.addressDetail = d[0].addressDetail
        addressData.district = d[0].district
        addressData.phone = d[0].phone
        addressData.realName = d[0].realName
      }
      else {
        addressData.addressDetail = res.data[0].addressDetail
        addressData.district = res.data[0].district
        addressData.phone = res.data[0].phone
        addressData.realName = res.data[0].realName
      }
    }
  })
}

function handleAddressClick() {
  uni.navigateTo({
    url: '/pages/addressPage/index?toSelect=true',
  })
}

function handleModalOk() {
  agree.value = true
  isModalShow.value = false
}
</script>

<template>
  <up-modal
    :show="isModalShow"
    confirm-text="同意"
    show-cancel-button
    close-on-click-overlay
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer :article-ids="[Article.labelPrint, Article.platform]" />
  </up-modal>
  <view class="flex items-end justify-between bg-white p-4">
    <view class="pl-2">
      <view class="o-color-primary text-xl font-bold">
        订单确认-{{ DESCRIPTION_STR[descriptionServiceType].title }}
      </view>
      <view class="o-color-aid text-sm">
        {{ DESCRIPTION_STR[descriptionServiceType].typeStr }}
      </view>
      <view class="text-xs">
        {{ DESCRIPTION_STR[descriptionServiceType].description }}
      </view>
    </view>
  </view>
  <view class="mt-3 px-4">
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 mt-2 font-bold">
        收货地址：
      </view>
      <view class="flex justify-between gap-4" @click="handleAddressClick">
        <view v-if="addressData.phone">
          <view class="flex gap-4">
            <text>{{ addressData.realName }}</text>
            <text>{{ addressData.phone }}</text>
          </view>
          <view class="text-sm">
            {{ addressData.district }}
          </view>
          <view class="text-sm">
            {{ addressData.addressDetail }}
          </view>
        </view>
        <view v-else class="color-gray">
          请添加地址
        </view>
        <view class="o-color-aid shrink-0 pr-1">
          <up-icon name="arrow-right" size="14" />
        </view>
      </view>
    </view>
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 mt-2 font-bold">
        所选方案：
      </view>
      <view class="flex justify-between gap-4">
        <view>{{ tempName }}</view>
        <view class="shrink-0">
          <text class="font-bold">
            {{ price }}
          </text>
          元
        </view>
      </view>
      <view class="o-line mb-3 mt-3" />
      <view class="flex justify-end">
        <view class="flex items-baseline">
          <view>合计：</view>
          <price-box :price="actuallyPrice" :size="48" class="o-color-danger" />
        </view>
      </view>
    </view>
    <view id="agreeElement" class="mt-6 flex items-center justify-center text-xs">
      <up-checkbox
        v-model:checked="agree"
        used-alone
        label-size="12"
        size="14"
        label="我已阅读并同意"
      />
      <view class="o-color-primary" @click.stop="isModalShow = true">
        《付款免责声明》
      </view>
    </view>
    <view class="p-11" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        :class="btnDisable ? 'o-bg-primary-disable' : ' o-bg-primary'"
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
        @click="handleSubmit"
      >
        {{ btnStr }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
