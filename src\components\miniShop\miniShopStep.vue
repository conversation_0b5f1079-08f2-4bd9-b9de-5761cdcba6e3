<script setup lang="ts">
import { handleCopy } from '@/utils'
</script>

<template>
  <view class="pt-2 mb-6">
    <view class="font-bold text-sm mb-2">开通步骤：</view>
    <view class="flex text-xs">
      <view class="f-index font-bold o-color-primary flex-shrink-0">1.</view>
      <view>按需求选择合适二维码微站服务方案，并开通。</view>
    </view>
    <view class="flex text-xs">
      <view class="f-index font-bold o-color-primary flex-shrink-0">2.</view>
      <view>完成付款后，用电脑打开 www.gs1helper.com 网站，并点击右上角登录按钮。</view>
    </view>
    <view
      class="flex gap-1 justify-center items-center py-2"
      @click="handleCopy('www.gs1helper.com')"
    >
      <view class="px-6 py-1 o-barcode-gray-card rd-1 ml-6">www.gs1helper.com</view>
      <up-tag size="mini" plain type="warning" text="点击复制"></up-tag>
    </view>
    <view class="flex text-xs">
      <view class="f-index font-bold o-color-primary flex-shrink-0">3.</view>
      <view>完成登录后，点击页面上方 二维码微站。</view>
    </view>
    <view class="flex text-xs">
      <view class="f-index font-bold o-color-primary flex-shrink-0">4.</view>
      <view>新建商品并上架后，即可下载二维码，扫码即可查看商品等详情。</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
