<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单开票',
    enablePullDownRefresh: true,
    backgroundColor: '#f0f3f8',
  },
}
</route>

<script lang="ts" setup>
import type { AllOrderPageRes, AllOrderPageResData } from '@/service/orderApi'
import { storeToRefs } from 'pinia'
import { makeFilmAndReportServiceDescription } from '@/components/descriptionStr'
import PriceBox from '@/components/Price/PriceBox.vue'
import { getServerTypeStr, InvoiceState, PayState, ServerType } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { OrderDirection } from '@/enums/httpEnum'
import { allOrderPageApi } from '@/service/orderApi'
import { invoiceOrderStore } from '@/store/invoiceOrderStore'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)

const useInvoiceOrderStore = invoiceOrderStore()
const { orderObjList, orderSuccess } = storeToRefs(useInvoiceOrderStore)
const showEmptyIcon = ref(false)
const orderCode = ref([])
const page = ref(1)
const list = ref<AllOrderPageResData[]>([])
// const selectValue = ref<AllOrderPageResData[]>([])
const filterList = [{ name: '全部' }, { name: '未开票' }, { name: '已开票' }]
const filterCurrent = ref(0)

function init() {
  return new Promise((resolve, reject) => {
    list.value = []
    orderCode.value = []
    orderObjList.value = []
    run()
      .then(() => {
        list.value = [...data.value]
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

onLoad(() => {
  // 页面加载,清空orderCode，后退到此页面时，不触发onLoad
  init()
})

onShow(() => {
  // 订单成功后退触发
  if (orderSuccess.value) {
    orderSuccess.value = false
    page.value = 1
    init()
  }
})

// 下拉刷新
onPullDownRefresh(() => {
  showEmptyIcon.value = false
  page.value = 1
  init().finally(() => {
    uni.stopPullDownRefresh()
  })
})

function getIsInvoiced() {
  switch (filterCurrent.value) {
    case 0:
      return null
    case 1:
      return 0
    case 2:
      return 1
    default:
      return null
  }
}

const { loading, error, data, run } = useRequest<AllOrderPageRes>(() =>
  allOrderPageApi({
    // serverType:
    isInvoiced: getIsInvoiced(),
    userId: userId.value,
    payState: PayState.paid,
    groupBy: '',
    needTotalCount: true,
    // orderBy: 'payDate',
    orderDirection: OrderDirection.desc,
    pageIndex: page.value,
    pageSize: 50,
  }),
)

// 滚到页面底部加载更多
onReachBottom(() => {
  if (!showEmptyIcon.value) {
    page.value++
    run().then(() => {
      list.value = [...list.value, ...data.value]
      if (data.value.length === 0) {
        showEmptyIcon.value = true
      }
    })
  }
})

function handleSelect(d: AllOrderPageResData) {
  // 发票状态，1：已开票，0：未申请,2：已申请,-1:废票
  if (!d.invoiceState) {
    if (orderCode.value.includes(d.orderCode)) {
      orderCode.value = orderCode.value.filter(item => item !== d.orderCode)
      // selectValue.value = selectValue.value.filter((item) => item.orderCode !== d.orderCode)
    }
    else {
      orderCode.value.push(d.orderCode)
      // selectValue.value.push(d)
    }
  }
}

function getStateColor(status: InvoiceState) {
  switch (status) {
    case InvoiceState.failed:
      return 'color-red'
    case InvoiceState.applying:
      return 'text-primary'
    case InvoiceState.done:
      return 'color-green'
    default:
      return 'color-gray'
  }
}

function handleSubmit() {
  if (orderCode.value.length > 0) {
    const length = orderCode.value.length
    let price = 0
    const selectValue = list.value.filter(item => orderCode.value.includes(item.orderCode))
    selectValue.forEach((subItem) => {
      price += subItem.actuallyPrice
    })
    price = Number(price.toFixed(2))
    orderObjList.value = selectValue.map((item) => {
      return {
        orderId: item.orderId,
        serverType: item.serverType,
      }
    })
    uni.navigateTo({
      url: `/pages/myOrder/invoiceInfo?length=${length}&price=${price}`,
    })
  }
}

function getDisable(invoiceState: number) {
  return invoiceState !== 0 && invoiceState !== null
}

function handleFilter() {
  page.value = 1
  init()
}
// TODO 下拉刷新用scrollView
// TODO 检查所有页面，当<up-checkbox>没用v-model时，点击slot内容触发，有没有跟点击框，割裂不同步值
// TODO 每个提交功能，是否有做防止重复点击提交
</script>

<template>
  <up-sticky bg-color="#fff">
    <up-tabs
      v-model:current="filterCurrent"
      :line-color="Color.blue"
      :line-width="90"
      :scrollable="false"
      :list="filterList"
      @change="handleFilter"
    />
  </up-sticky>
  <view class="px-4 pb-10">
    <up-checkbox-group v-model="orderCode" placement="column">
      <view
        v-for="item in list"
        :key="`${item.serverType}${item.orderId}`"
        class="mt-3 rd-2 bg-white p-4"
      >
        <view class="flex items-center" @click="handleSelect(item)">
          <up-checkbox :name="item.orderCode" :disabled="!!item.invoiceState" />
          <view class="flex-grow-1">
            <view class="o-color-aid text-xs">
              成交时间：{{ item.payDate }}
            </view>
            <view class="o-color-aid text-xs">
              订单编号：{{ item.orderCode }}
            </view>
            <!--          <view class="o-color-aid text-xs">订单类型：{{ getServerTypeStr(item.serverType) }}</view> -->
          </view>
          <view
            v-if="getDisable(item.invoiceState)"
            class="font-bold"
            :class="getStateColor(item.invoiceState)"
          >
            {{ item.invoiceStateStr }}
          </view>
        </view>
        <view class="o-line mb-2 mt-2" />
        <view class="mb-1 font-bold">
          {{ getServerTypeStr(item.serverType) }}：
        </view>
        <view v-if="item.serverType === ServerType.miniShop">
          {{ item.qrOrderParamDTO?.tempName }}
        </view>
        <view
          v-if="
            [
              ServerType.infoReport,
              ServerType.designServer,
              ServerType.labelPrint,
              ServerType.registerService,
              ServerType.modifyService,
              ServerType.renewalService,
              ServerType.importedGoods,
            ].includes(item.serverType)
          "
        >
          {{ item.otherOrderParamDTO?.orderContent }}
        </view>
        <view
          v-if="[ServerType.makeFilm, ServerType.storeCode].includes(item.serverType)"
          class="o-row-scroll mb-2 flex items-center"
        >
          <view class="o-barcode-gray-card rd-1">
            {{ item.barOrderParamDTO?.startBarCode }}
          </view>
          <template
            v-if="item.barOrderParamDTO?.endBarCode !== item.barOrderParamDTO?.startBarCode"
          >
            <view>~</view>
            <view class="o-barcode-gray-card rd-1">
              {{ item.barOrderParamDTO?.endBarCode }}
            </view>
          </template>
        </view>
        <view class="flex items-end justify-between gap-3">
          <view
            v-if="[ServerType.makeFilm, ServerType.storeCode].includes(item.serverType)"
            class="text-xs color-gray"
          >
            <view class="">
              放大系数为：{{ item.barOrderParamDTO?.size }}
            </view>
            <view>
              {{ item.number }}张
              <text>×</text>
              {{ item.price }}元/张
            </view>
            <view v-if="item.isHasOtherServer">
              含{{ makeFilmAndReportServiceDescription }}
            </view>
          </view>
          <view v-if="item.serverType === ServerType.infoReport" class="text-xs color-gray">
            {{ item.price }}元/批
          </view>
          <view v-else />
          <view class="flex shrink-0 justify-end">
            <view class="flex items-baseline">
              <view class="text-sm">
                实付：
              </view>
              <price-box :price="item.actuallyPrice" :size="36" />
            </view>
          </view>
        </view>
      </view>
    </up-checkbox-group>
    <up-empty
      v-if="list?.length === 0"
      icon="https://wx.gs1helper.com/images/common/search.png"
      text="暂无订单"
    />
  </view>
  <view v-if="showEmptyIcon" class="o-color-aid w-full text-center">
    - 已经到底了 -
  </view>
  <view class="p-10" />
  <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
    <view
      class="flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
      :class="orderCode.length > 0 ? 'o-bg-primary' : 'o-bg-primary-disable'"
      @click="handleSubmit"
    >
      {{ orderCode.length > 1 ? '合并开票' : '开票' }}
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
