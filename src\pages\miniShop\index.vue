<route lang="json5">
{
  style: {
    navigationBarTitleText: '二维码微站',
  },
}
</route>

<script lang="ts" setup>
import type { CreateQrOrderParams } from '@/service/orderApi'
import { storeToRefs } from 'pinia'
import { DesignImage } from '@/components/image'
import MiniShopStep from '@/components/miniShop/miniShopStep.vue'
import PriceBox from '@/components/Price/PriceBox.vue'
import ServerSwiper from '@/components/serverSwiper.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { FromPlatform } from '@/enums'
import { createQrOrderApi, memberLevelTempListApi } from '@/service/orderApi'
import { msgModalStore } from '@/store/msgModalStore'
import { miniShopOrderStore } from '@/store/orderStore/miniShopOrderStore'
import { useUserStore } from '@/store/user'
import { getMiniShopTempTypeStr } from '@/utils'

/* defineOptions({
  name: 'MakeFilm',
}) */

const useMsgModalStore = msgModalStore()

const useOrderStore = miniShopOrderStore()
const { orderCode, tempName, tempType, capacity, flow } = storeToRefs(useOrderStore)

const userStore = useUserStore()
userStore.login()

const qrImg = ref('')
const loading = ref(false)
const isShowStep = ref(true)
const serveData = ref<any[]>([])
const selectTempId = ref()

qrImg.value = 'https://wx.gs1helper.com/images/miniShop_Qr_template.png'

memberLevelTempListApi().then((res: any) => {
  serveData.value = res.data
})

function handleSubmit() {
  // couponId
  if (!selectTempId.value) {
    useMsgModalStore.alert({ title: '请选择方案' })
    return
  }
  loading.value = true
  uni.showLoading({ title: '订单提交中...' })
  const params: CreateQrOrderParams = {
    number: 1,
    tempId: selectTempId.value,
    userId: useUserStore().userId,
  }
  // #ifdef MP-WEIXIN
  params.fromTo = import.meta.env.VITE_FROM_PLATFORM
  // #endif
  // #ifdef MP-TOUTIAO
  params.fromTo = FromPlatform.douYin
  // #endif
  createQrOrderApi(params)
    .then((res) => {
      const d = res.data
      orderCode.value = d.orderCode
      tempType.value = d.tempType
      capacity.value = d.capacity
      flow.value = d.flow
      loading.value = false
      uni.hideLoading()
      uni.navigateTo({
        url: '/pages/orderConfirm/miniShop',
      })
    })
    .catch(() => {
      loading.value = false
      uni.hideLoading()
    })
}

function handleSelect(d: any) {
  selectTempId.value = d.tempId
  tempName.value = d.tempName
}

function handlePreview() {
  uni.navigateTo({
    url:
      `/pages/miniShop/viewWebPage?url=${
        encodeURIComponent('https://qr.gs1helper.com/gtin/06900000000007/gs1minishopcode/1005')}`,
  })
}
</script>

<template>
  <server-swiper :carouses-list="DesignImage.miniShop.carouses" />
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="center flex-col rd-2 bg-white p-4">
      <view class="mb-2 w-full text-left text-sm font-bold">
        示例展示：
      </view>
      <view class="o-border mb-2 rd-2 px-6 py-2 text-sm text-primary" @click="handlePreview">
        点击浏览示例
      </view>
      <up-image :width="200" :height="200" :src="qrImg" alt="二维码" :show-menu-by-longpress="true" />
      <view class="text-xs color-gray">
        或长按保存后，用浏览器扫码
      </view>
    </view>
  </view>

  <view class="mt-3 px-4">
    <view class="flex flex-col items-center justify-center rd-2 bg-white p-4">
      <mini-shop-step v-if="isShowStep" />
      <view class="o-color-aid flex gap-3 text-xs" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowStep ? '隐藏' : '显示' }}开通步骤说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>

    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 pt-4 font-bold">
        服务方案：
      </view>
      <view id="targetElement" class="mt-4">
        <up-radio-group v-model="selectTempId" class="o-vf-up-radio-group" placement="column">
          <up-radio
            v-for="(item, index) in serveData"
            :key="index"
            :name="item.tempId"
            @change="handleSelect(item)"
          >
            <template #label>
              <view class="f-serve mt-2 flex items-center justify-between">
                <view class="shrink-0">
                  <view>{{ item.tempName }}</view>
                  <view class="color-gray">
                    含{{ item.capacity }}G存储空间
                  </view>
                </view>
                <view class="inline-block flex shrink-0 flex-col items-end">
                  <view class="flex items-baseline">
                    <price-box
                      :price="item.discountPrice"
                      not-show-decimal
                      :size="40"
                      class="color-red"
                    />
                    <view class="text-xs color-gray">
                      /{{ getMiniShopTempTypeStr(item.tempType) }}
                    </view>
                  </view>
                  <price-box
                    v-if="Math.abs(item.discountPrice - item.originalPrice) > Number.EPSILON"
                    :price="item.originalPrice"
                    cancel
                    thin
                    not-show-decimal
                    :size="32"
                    class="color-gray"
                  />
                </view>
              </view>
            </template>
          </up-radio>
        </up-radio-group>
      </view>
    </view>
    <view class="p-10" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-serve {
  width: 540rpx;
}
</style>
