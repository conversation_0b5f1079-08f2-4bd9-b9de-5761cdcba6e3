<route lang="json5">
{ style: { navigationBarTitleText: '产品编码信息通报' } }
</route>

<script lang="ts" setup>
import CsLongButton from '@/components/customer/csLongButton.vue'
import ReportStep from '@/components/infoReport/reportStep.vue'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { useCreateOrder } from '@/hooks/useCreateOrder'

const { loading, isShowStep, serveData, selectServe, handleSubmit } = useCreateOrder({
  orderConfirmUrl: '/pages/orderConfirm/infoReport',
})
</script>

<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="flex flex-col items-center justify-center rd-2 bg-white p-4">
      <report-step v-if="isShowStep" />
      <view class="o-color-aid flex gap-3 text-xs" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowStep ? '隐藏' : '显示' }}通报步骤说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 pt-4 font-bold">
        服务方案：
      </view>
      <view id="targetElement" class="mt-4">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
    </view>
    <view class="o-color-aid mt-4 text-center text-xs">
      <text class="o-color-danger pr-2">
        *
      </text>
      更多数量请分多次订单
    </view>
    <view class="p-10" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
        @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
