<route lang="json5">
{
  style: {
    navigationBarTitleText: '包装设计',
  },
}
</route>

<script lang="ts" setup>
import CsLongButton from '@/components/customer/csLongButton.vue'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { DesignImage } from '@/components/image'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'
import ServerSwiper from '@/components/serverSwiper.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { useCreateOrder } from '@/hooks/useCreateOrder'

const RESOURCES_URL = import.meta.env.VITE_RESOURCES_URL

const {
  loading,
  isShowStep,
  customPrice,
  serveData,
  selectServe,
  descriptionServiceType,
  handleSubmit,
} = useCreateOrder({
  orderConfirmUrl: '/pages/orderConfirm/default',
  paymentSuccessUrl: '/pages/paymentSuccess/designServer',
  isMustCustomPrice: true,
  noPriceCallBack: () => {
    uni.pageScrollTo({ selector: '#price' })
  },
})
const isShowDemo = ref(false)

const downloadDescription = [
  '选择一张喜欢的模板。',
  '付款后，联系客服获取想要的那张模板。',
  '获取的模板是可编辑的源文件。',
  '可自行将模板中的文字、logo替换成合适的内容。',
]

const labelDemo = [
  `${RESOURCES_URL}/labelDesign/label_001.jpg`,
  `${RESOURCES_URL}/labelDesign/label_002.jpg`,
  `${RESOURCES_URL}/labelDesign/label_003.jpg`,
]

function previewImg(index: number) {
  uni.previewImage({
    urls: labelDemo,
    current: index,
  })
}
</script>

<template>
  <server-swiper :carouses-list="DesignImage.designServer.carouses" />
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="flex flex-col items-center justify-center rd-2 bg-white p-4">
      <view v-if="isShowStep" class="mb-6 pt-2">
        <view class="mb-2 text-sm font-bold">
          印前合规检查步骤：
        </view>
        <view
          v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType].makeStep"
          :key="index"
          class="flex text-xs"
        >
          <view class="f-index o-color-primary flex-shrink-0 font-bold">
            {{ index + 1 }}.
          </view>
          <view>{{ item }}</view>
        </view>
        <view class="mb-2 mt-6 text-sm font-bold">
          设计服务步骤：
        </view>
        <view
          v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType].designStep"
          :key="index"
          class="flex text-xs"
        >
          <view class="f-index o-color-primary flex-shrink-0 font-bold">
            {{ index + 1 }}.
          </view>
          <view>{{ item }}</view>
        </view>
      </view>
      <view class="o-color-aid flex gap-3 text-xs" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowStep ? '隐藏' : '显示' }}步骤说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>
    <view class="mt-3 rd-2 bg-white py-4">
      <view class="mb-6 px-4 pt-2">
        <view class="mb-2 text-sm font-bold">
          自助模板下载：
        </view>
        <view v-for="(item, index) in downloadDescription" :key="index" class="flex text-xs">
          <view class="f-index o-color-primary flex-shrink-0 font-bold">
            {{ index + 1 }}.
          </view>
          <view>{{ item }}</view>
        </view>
      </view>
      <view class="relative overflow-hidden" :class="isShowDemo ? '' : 'h-50vw'">
        <up-image
          v-for="(item, index) in labelDemo"
          :key="index"
          width="100%"
          mode="widthFix"
          :src="item"
          @click="previewImg(index)"
        />
        <view v-show="!isShowDemo" class="f-list-hidden absolute bottom-0 left-0 z-1 w-full" />
      </view>
      <view
        class="o-color-aid w-full flex items-center justify-center gap-3 text-xs"
        @click="isShowDemo = !isShowDemo"
      >
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowDemo ? '隐藏' : '显示' }}自助模板</view>
        <up-icon v-if="isShowDemo" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 pt-2 font-bold">
        选择服务：
      </view>
      <view id="targetElement" class="mt-4">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
      <view class="mb-2 mt-5 text-sm font-bold">
        请先联系客服，沟通好价格后，填写金额：
      </view>
      <view class="flex">
        <view id="price" class="f-label mt-2 shrink-0 text-sm">
          <text class="o-color-danger pr-1">
            *
          </text>
          协定金额：
        </view>
        <up-input v-model="customPrice" border="bottom" :maxlength="10" type="number" clearable />
        <view class="mt-2 text-sm">
          元
        </view>
      </view>
    </view>
    <view class="p-10" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-label {
  flex-shrink: 0;
  width: 26vw;
}

.f-index {
  width: 1.2rem;
}

.f-w {
  bottom: -0.8rem;
  left: 100rpx;
}

.f-h {
  top: 70rpx;
  left: -2.5rem;
  transform: rotate(-90deg);
}

:deep(.f-table) {
  // border-bottom: 1px solid #e4e4e4;
  &:nth-of-type(even) {
    background-color: var(--o-body-bg-color);
  }
}

.f-serve {
  width: 540rpx;
}

.f-list-hidden {
  height: 13vw;
  background: linear-gradient(rgba(255, 255, 255, 0) 0%, #fff 70%);
}
</style>
