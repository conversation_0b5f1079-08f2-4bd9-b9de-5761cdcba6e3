<route lang="json5">
{
  style: {
    navigationBarTitleText: '进口商品报备',
  },
}
</route>

<script lang="ts" setup>
import ServerTitle from '@/components/serverTitle.vue'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { useCreateOrder } from '@/hooks/useCreateOrder'
import CsLongButton from '@/components/customer/csLongButton.vue'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'

const { loading, isShowStep, serveData, selectServe, descriptionServiceType, handleSubmit } =
  useCreateOrder({ orderConfirmUrl: '/pages/orderConfirm/default' })
</script>
<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rd-2 flex flex-col items-center justify-center">
      <view v-if="isShowStep" class="pt-2 mb-6">
        <view class="font-bold text-sm mb-2">报备说明：</view>
        <view
          v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType]?.makeStep"
          :key="index"
          class="flex text-xs"
        >
          <view class="f-index font-bold o-color-primary flex-shrink-0">{{ index + 1 }}.</view>
          <view>{{ item }}</view>
        </view>
      </view>
      <view class="flex gap-3 text-xs o-color-aid" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18"></up-icon>
        <view>{{ isShowStep ? '隐藏' : '显示' }}报备说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16"></up-icon>
        <up-icon v-else name="arrow-down" size="16"></up-icon>
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 bg-white p-4 rd-2">
      <view class="font-bold pt-4 mb-3">服务方案：</view>
      <view class="mt-4" id="targetElement">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
    </view>
    <view class="text-xs text-center o-color-aid mt-4">
      <text class="o-color-danger pr-2">*</text>
      更多数量请联系客服
    </view>
    <view class="p-10"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view
        class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rd-2"
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-label {
  flex-shrink: 0;
  width: 33vw;
}

.f-index {
  width: 1.2rem;
}

.f-w {
  bottom: -0.8rem;
  left: 100rpx;
}

.f-h {
  top: 70rpx;
  left: -2.5rem;
  transform: rotate(-90deg);
}

:deep(.f-table) {
  // border-bottom: 1px solid #e4e4e4;
  &:nth-of-type(even) {
    background-color: var(--o-body-bg-color);
  }
}

.f-serve {
  width: 540rpx;
}
</style>
