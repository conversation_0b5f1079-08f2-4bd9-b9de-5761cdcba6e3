<script setup lang="ts">
import { tabbarStore } from './tabbar'

// 'i-carbon-code',
import { tabbarList as _tabBarList, cacheTabbarEnable, selectedTabbarStrategy, TABBAR_MAP } from './tabbarList'

const RESOURCES_URL = import.meta.env.VITE_RESOURCES_URL

const customTabbarEnable
  = selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE
    || selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITHOUT_CACHE

/** tabbarList 里面的 path 从 pages.config.ts 得到 */
const tabbarList = _tabBarList.map(item => ({ ...item, path: `/${item.pagePath}` }))

function selectTabBar(name: number) {
  const url = tabbarList[name].path
  tabbarStore.setCurIdx(name)
  if (cacheTabbarEnable) {
    uni.switchTab({ url })
  }
  else {
    uni.navigateTo({ url })
  }
}
onLoad(() => {
  // 解决原生 tabBar 未隐藏导致有2个 tabBar 的问题
  const hideRedundantTabbarEnable = selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE
  hideRedundantTabbarEnable
  && uni.hideTabBar({
    fail(err) {
      console.log('hideTabBar fail: ', err)
    },
    success(res) {
      console.log('hideTabBar success: ', res)
    },
  })
})

function handleTabbarClick(index: number) {
  selectTabBar(index)
}
</script>

<template>
  <view
    v-if="customTabbarEnable" class="fixed bottom-0 left-0 right-0 z-100 box-border w-full center px-3 pb-safe"
  >
    <view class="f-tabbar mb-2 flex flex-1 items-center justify-around overflow-hidden bg-white px-4">
      <view v-for="(item, index) in tabbarList" :key="index" @click="handleTabbarClick(index)">
        <image class="f-image" :src="tabbarStore.curIdx === index ? item.selectedIconPath : item.iconPath" mode="aspectFit" />
        <!-- 预加载未选中和选中状态的图标 -->
        <image class="f-hidden" :src="item.iconPath" />
        <image class="f-hidden" :src="item.selectedIconPath" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-tabbar {
  $h: 156rpx;
  height: $h;
  border-radius: $h;
  box-shadow: 0 20rpx 40rpx -20rpx rgba(183, 210, 255, 0.8);
}
.f-image {
  $w: 130rpx;
  width: $w;
  height: $w;
}

.f-hidden {
  width: 0;
  height: 0;
  opacity: 0;
  position: absolute;
  visibility: hidden;
}
</style>
