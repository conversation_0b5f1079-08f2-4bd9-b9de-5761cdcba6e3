/**
 * tabbar 选择的策略，更详细的介绍见 tabbar.md 文件
 * 0: 'NO_TABBAR' `无 tabbar`
 * 1: 'NATIVE_TABBAR'  `完全原生 tabbar`
 * 2: 'CUSTOM_TABBAR_WITH_CACHE' `有缓存自定义 tabbar`
 * 3: 'CUSTOM_TABBAR_WITHOUT_CACHE' `无缓存自定义 tabbar`
 *
 * 温馨提示：本文件的任何代码更改了之后，都需要重新运行，否则 pages.json 不会更新导致错误
 */
export const TABBAR_MAP = {
  NO_TABBAR: 0,
  NATIVE_TABBAR: 1,
  CUSTOM_TABBAR_WITH_CACHE: 2,
  CUSTOM_TABBAR_WITHOUT_CACHE: 3,
}
// TODO：通过这里切换使用tabbar的策略
export const selectedTabbarStrategy = TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE

// selectedTabbarStrategy==NATIVE_TABBAR(1) 时，需要填 iconPath 和 selectedIconPath
// selectedTabbarStrategy==CUSTOM_TABBAR(2,3) 时，需要填 icon 和 iconType
// selectedTabbarStrategy==NO_TABBAR(0) 时，tabbarList 不生效
export const tabbarList = [
  /*   {
    iconPath: 'static/tabbar/home.png',
    selectedIconPath: 'static/tabbar/homeHL.png',
    pagePath: 'pages/index/index',
    text: '首页',
    icon: 'home',
    // 选用 UI  框架自带的 icon时，iconType 为 uiLib
    iconType: 'uiLib',
  },
  {
    iconPath: 'static/tabbar/example.png',
    selectedIconPath: 'static/tabbar/exampleHL.png',
    pagePath: 'pages/about/about',
    text: '关于',
    icon: 'i-carbon-code',
    // 注意 unocss 的图标需要在 页面上引入一下，或者配置到 unocss.config.ts 的 safelist 中
    iconType: 'unocss',
  }, */
  {
    // 首页
    iconPath: '/static/tabbar/home.png',
    selectedIconPath: '/static/tabbar/homeH.png',
    pagePath: 'pages/index/index',
    text: '',
    iconType: 'uiLib',
    icon: '',
  },
  {
    // 条码管理
    iconPath: '/static/tabbar/code.png',
    selectedIconPath: '/static/tabbar/codeH.png',
    pagePath: 'pages/myFilm/index',
    text: '',
    iconType: 'uiLib',
    icon: '',
  },
  {
    // 产品通报
    iconPath: '/static/tabbar/product.png',
    selectedIconPath: '/static/tabbar/productH.png',
    pagePath: 'pages/infoReportMg/index',
    text: '',
    iconType: 'uiLib',
    icon: '',
  },
  {
    // 我的
    iconPath: '/static/tabbar/user.png',
    selectedIconPath: '/static/tabbar/userH.png',
    pagePath: 'pages/userPage/index',
    text: '',
    iconType: 'uiLib',
    icon: '',
  },
/*   {
    iconPath: 'static/tabbar/home.png',
    selectedIconPath: 'static/tabbar/homeH.png',
    pagePath: 'pages/index/index',
    text: '业务办理',
    iconType: 'uiLib',
    icon: '',
  },
  {
    iconPath: 'static/tabbar/mine.png',
    selectedIconPath: 'static/tabbar/mineH.png',
    pagePath: 'pages/myFilm/index',
    text: '我的条码',
    iconType: 'uiLib',
    icon: '',
  },
  {
    iconPath: 'static/tabbar/user.png',
    selectedIconPath: 'static/tabbar/userH.png',
    pagePath: 'pages/userPage/index',
    text: '用户信息',
    iconType: 'uiLib',
    icon: '',
  }, */
]

// NATIVE_TABBAR(1) 和 CUSTOM_TABBAR_WITH_CACHE(2) 时，需要tabbar缓存
export const cacheTabbarEnable = selectedTabbarStrategy === TABBAR_MAP.NATIVE_TABBAR
  || selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE

const _tabbar = {
  color: '#999999',
  selectedColor: '#165dff',
  backgroundColor: '#F8F8F8',
  borderStyle: 'white',
  height: '1px',
  fontSize: '10px',
  iconWidth: '24px',
  spacing: '3px',
  list: tabbarList,
}

// 0和1 需要显示底部的tabbar的各种配置，以利用缓存
export const tabBar = cacheTabbarEnable ? _tabbar : undefined
