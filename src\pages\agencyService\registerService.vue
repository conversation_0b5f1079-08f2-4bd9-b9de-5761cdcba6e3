<route lang="json5">
{
  style: {
    navigationBarTitleText: '条码注册申请',
  },
}
</route>

<script lang="ts" setup>
import ServerTitle from '@/components/serverTitle.vue'
import { useCreateOrder } from '@/hooks/useCreateOrder'
import CsLongButton from '@/components/customer/csLongButton.vue'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'
import RegisterStep from '@/components/agencyService/registerStep.vue'
import { toAgencyPath } from '@/utils/'
import { ServerType } from '@/enums'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { existUncompletedRegister } = storeToRefs(userStore)

const { loading, isShowStep, serveData, selectServe, descriptionServiceType, handleSubmit } =
  useCreateOrder({
    orderConfirmUrl: '/pages/orderConfirm/default',
    paymentSuccessUrl: '/pages/paymentSuccess/agencyService',
  })
</script>
<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="bg-white p-4 rd-2 flex flex-col items-center justify-center">
      <view v-if="isShowStep" class="pt-2 mb-6">
        <RegisterStep />
      </view>
      <view class="flex gap-3 text-xs o-color-aid" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18"></up-icon>
        <view>{{ isShowStep ? '隐藏' : '显示' }}办理说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16"></up-icon>
        <up-icon v-else name="arrow-down" size="16"></up-icon>
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 bg-white p-4 rd-2">
      <view class="font-bold text-xs mb-2 color-red">关于条码容量：</view>
      <view class="o-p text-xs">
        新注册用户，条码容量为
        <text class="font-bold">1000</text>
        个，即最多只能拥有1000个条码编码。
      </view>
      <view class="o-p text-xs mt-1">容量并非具体条码编号，具体每个条码可在本小程序另行制作。</view>
    </view>
    <view class="mt-3 bg-white p-4 rd-2">
      <view class="font-bold pt-4 mb-3">服务方案：</view>
      <view class="mt-4" id="targetElement">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
    </view>
    <view class="p-10"></view>
    <view class="box-border fixed w-full p-4 left-0 bottom-0 z-10">
      <view class="flex gap-1">
        <view
          class="py-3 flex-grow-1 flex items-center justify-center font-bold rd-2"
          :class="
            existUncompletedRegister
              ? 'bg-white o-color-primary border'
              : 'o-bg-primary color-white'
          "
          @click="handleSubmit"
        >
          {{ existUncompletedRegister ? '继续下单' : '提交方案' }}
        </view>
        <view
          v-if="existUncompletedRegister"
          class="py-3 flex-grow-1 o-bg-primary color-white rd-2 flex justify-center items-baseline"
          @click="toAgencyPath(ServerType.registerService)"
        >
          <text class="text-sm">您有未完成的业务，</text>
          <text class="font-bold">前往办理</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
