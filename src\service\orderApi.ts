import type { BarType, CouponType, FromPlatform, InvoiceState, ServerType } from '@/enums'
import type { OrderDirection } from '@/enums/httpEnum'
import { http } from '@/utils/http'

// 参数接口
export interface CreateOrderParams {
  barType: string
  endProjectCode: string
  fromTo?: FromPlatform
  packageCode?: number
  serverType: ServerType
  size: number
  startProjectCode: string
  userId: number
  vendorCode: string
}

// 响应接口
export interface CreateOrderRes {
  data: {
    endBarCode: string
    existCount: number
    orderCode: string
    orderId: number
    price: number
    serverType: ServerType
    size: number
    startBarCode: string
    total: number
    totalPrice: number
    useCount: number
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 创建预览订单
 * @param {object} params cmd
 * @param {string} params.barType 条码类型：EAN13/ITF14
 * @param {string} params.endProjectCode 结束项目代码
 * @param {number} params.packageCode 包装指示符
 * @param {number} params.serverType 服务类型：1：条码制作，2：信息上报,3:包装设计，4、标签印刷，5：店内码，一定要传
 * @param {number} params.size 放大系数
 * @param {string} params.startProjectCode 开始项目代码
 * @param {number} params.userId 用户id
 * @param {string} params.vendorCode 厂商识别码
 * @returns
 */
export function createOrderApi(params: CreateOrderParams) {
  return http.post<CreateOrderRes>('/api/createOrder', params)
}

// 参数接口
export interface CreateOrderV2Params {
  barType: BarType
  endProjectCode: string
  fromTo?: FromPlatform
  isHasOtherServer: boolean
  packageCode?: number
  serverType: ServerType
  size: number
  startProjectCode: string
  userId: number
  vendorCode: string
}

// 响应接口
export interface CreateOrderV2Res {
  data: {
    barCodePrice: number
    endBarCode: string
    existCount: number
    extraPrice: number
    isFirstCode: boolean
    isHasOtherServer: boolean
    orderCode: string
    orderId: number
    price: number
    serverType: ServerType
    size: number
    startBarCode: string
    total: number
    totalPrice: number
    useCount: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 创建预览订单_V2.0，包含条码跟单次通报
 * @param {object} params cmd
 * @param {string} params.barType 条码类型：EAN13/ITF14
 * @param {string} params.endProjectCode 结束项目代码
 * @param {boolean} params.isHasOtherServer 是否包含通报信息,true:包含，false:不包含
 * @param {number} params.packageCode 包装指示符
 * @param {number} params.serverType 服务类型：1：条码制作，2：信息上报,3:包装设计，4、标签印刷，5：店内码，6：代办业务；7：标签设计；8：打印机；，一定要传
 * @param {number} params.size 放大系数
 * @param {string} params.startProjectCode 开始项目代码
 * @param {number} params.userId 用户id
 * @param {string} params.vendorCode 厂商识别码
 * @returns
 */
export function createOrderV2Api(params: CreateOrderV2Params) {
  return http.post<CreateOrderV2Res>('/api/createOrderV2', params)
}

// 参数接口
export interface OrderPageParams {
  groupBy?: string
  isInvoiced?: number
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirectionType
  pageIndex?: number
  pageSize?: number
  payState?: number
  serverType?: ServerType
  userId: number
}

// 响应接口
export interface OrderPageRes {
  data: {
    actuallyPrice: number
    createdDate: Record<string, unknown>
    endBarCode: string
    invoiceId: number
    invoiceState: InvoiceState
    invoiceStateStr: string
    number: number
    orderCode: string
    orderContent: string
    orderId: number
    payDate: Record<string, unknown>
    payState: number
    payStateStr: string
    price: number
    serverType: ServerType
    size: number
    startBarCode: string
    totalPrice: number
    transactionNo: string
    userId: number
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}

/**
 * 订单分页
 * @param {object} params qry
 * @param {string} params.groupBy
 * @param {number} params.isInvoiced 发票状态，1：已开票，0：未开票
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.payState 支付状态，-1:新建；0：未支付，1：已支付（支付成功），2：支付失败，
 * @param {number} params.serverType 服务类型：1：条码制作，2：信息上报
 * @param {number} params.userId 用户id
 * @returns
 */
export function orderPageApi(params: OrderPageParams) {
  return http.post<OrderPageRes>('/api/orderPage', params)
}

// 参数接口
export interface DownloadInvoiceParams {
  address?: string
  bank?: string
  bankCode?: string
  barOrderIdList: Record<string, unknown>[]
  companyName: string
  contact: string
  contactPhone: string
  creditCode: string
  downloadType: 1 | 2 // 1:直接下载，2：发送到邮箱
  email?: string
  invoiceTempId: number
  phone?: string
  userId: number
}

// 响应接口
export interface DownloadInvoiceRes {
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 申请开发票
 * @param {object} params cmd
 * @param {string} params.address 注册地址
 * @param {string} params.bank 开户银行
 * @param {string} params.bankCode 银行账号
 * @param {Array} params.barOrderIdList 订单is数组
 * @param {string} params.companyName 企业名称,发票抬头,下单获取发票填写
 * @param {string} params.contact 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.creditCode 企业统一信用代码
 * @param {number} params.downloadType 下载方式:1:直接下载，2：发送到邮箱
 * @param {string} params.email 邮箱
 * @param {number} params.invoiceTempId 发票模版id
 * @param {string} params.phone 注册电话
 * @param {number} params.userId 用户id
 * @returns
 */
export function downloadInvoiceApi(params: DownloadInvoiceParams) {
  return http.post<DownloadInvoiceRes>('/api/downloadInvoice', params)
}

// 参数接口
export interface PayOrderParams {
  fromTo?: FromPlatform
  orderCode: string
}

// 响应接口
export interface PayOrderRes {
  data: {
    currencyType: string
    fee: number
    isNeedToPay: boolean
    nonceStr: string
    packageValue: string
    paySign: string
    paymentArgs: Record<string, unknown>
    signType: string
    timeStamp: string
    version: string
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 微信支付订单，回传前端支付所需参数
 * @param {object} params cmd
 * @param {string} params.orderCode 订单编号
 * @returns
 */
export function payOrderApi(params: PayOrderParams) {
  return http.post<PayOrderRes>('/api/payOrder', params)
}

// 参数接口
export interface CouponPageParams {
  groupBy?: string
  isTimeOut?: number
  isUsed?: number
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirectionType
  pageIndex?: number
  pageSize?: number
  serverType?: ServerType // 服务类型，1：条码制作，2：信息上报
  userId: number
}

export interface CouponPageResData {
  couponId: number
  couponName: string
  couponPrice: number
  couponType: CouponType
  description: string
  discount: number
  expiryDay: number
  expirySeconds: number
  grantDate: string
  isTimeOut: number
  isUsed: number
  minUsagePrice: number
  orderId: number
  remark: string
  serverType: ServerType
  userId: number
  vendorCode: string
}

// 响应接口
export interface CouponPageRes {
  data: CouponPageResData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}

/**
 * 用户的优惠券分页
 * @param {object} params qry
 * @param {string} params.groupBy
 * @param {number} params.isTimeOut 是否过期：1:是，0:否
 * @param {number} params.isUsed 是否已使用：1:是，0:否
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.serverType 服务类型，1：条码制作，2：信息上报
 * @param {number} params.userId 用户id
 * @returns
 */
export function couponPageApi(params: CouponPageParams) {
  return http.post<CouponPageRes>('/api/couponPage', params)
}

// 参数接口
export interface SumbitOrderParams {
  couponId?: number
  orderCode: string
  userPhone?: string
}

// 响应接口
export interface SumbitOrderRes {
  data: {
    actuallyPrice: number
    discountsPrice: number
    endBarCode: string
    existCount: number
    orderCode: string
    orderId: number
    price: number
    serverType: number
    size: number
    startBarCode: string
    total: number
    totalPrice: number
    useCount: number
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 提交订单
 * @param {object} params cmd
 * @param {number} params.couponId 优惠券id
 * @param {string} params.orderCode 订单Code
 * @param {string} params.userPhone 用户电话
 * @returns
 */
export function sumbitOrderApi(params: SumbitOrderParams) {
  return http.post<SumbitOrderRes>('/api/sumbitOrder', params)
}

// 参数接口
export interface GetCouponParams {
  orderCode: string
}

// 响应接口
export interface GetCouponRes {
  data: {
    couponId: number
    couponName: string
    couponPrice: number
    couponType: CouponType
    description: string
    discount: number
    expirySeconds: number
    grantDate: Record<string, unknown>
    isDel: number
    isUsed: number
    minUsagePrice: number
    orderId: number
    remark: string
    userId: number
    vendorCode: string
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 根据订单号获取对应的优惠券
 * @param {object} params qry
 * @param {string} params.orderCode 订单编号
 * @returns
 */
export function getCouponApi(params: GetCouponParams) {
  return http.post<GetCouponRes>('/api/getCoupon', params)
}

// 参数接口
interface PriceTempListParams {
  tempType: ServerType // 价格模版类型：1：条码制作,2:信息上报,3:包装设计，4、标签设计
  isDefault?: 1 | 0 // 是否首张特价,1：是，0：否
}

export interface PriceTempListResData {
  endNum: number
  isDefault: number
  price: number
  priceTempId: number
  startNum: number
  tempName: string
  tempSort: number
  tempUnit: string
  tempType: ServerType
  label: string
}

// 响应接口
export interface PriceTempListRes {
  data: PriceTempListResData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}

/**
 * 价格模板列表
 * @param params
 */
export function priceTempListApi(params: PriceTempListParams) {
  return http.post<PriceTempListRes>('/api/priceTempList', params)
}

// 参数接口
export interface CreateReportOrderParams {
  fromTo?: FromPlatform
  isHasChange?: boolean // 续展是否包含变更，true:包含，false:不包含
  price?: number
  priceTempId: number
  serverType?: ServerType
  userId: number
}

// 响应接口
export interface CreateReportOrderRes {
  data: {
    orderCode: string
    orderId: number
    price: number
    priceUnit: string
    serverType: ServerType
    tempName: string
    tempType: number
    total: number
    totalPrice: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 创建预览订单
 * @param {object} params cmd
 * @param {number} params.fromTo 来源，1：微信小程序、2：抖音小程序、3：其他平台
 * @param {boolean} params.isHasChange 续展是否包含变更，true:包含，false:不包含
 * @param {number} params.price 单价，设计类价格可能会变动，允许可以手动填写
 * @param {number} params.priceTempId 方案模版ID
 * @param {number} params.serverType 服务类型：1：条码制作，2：信息上报,3:包装设计，4、标签印刷，5：店内码，6：续展；10：注册；12：变更；7：标签设计；8：打印机；9：进口商品报备；一定要传
 * @param {number} params.userId 用户id
 * @returns
 */
export function createReportOrderApi(params: CreateReportOrderParams) {
  return http.post<CreateReportOrderRes>('/api/createReportOrder', params)
}

// 参数接口
export interface SumbitReportOrderParams {
  addressDetail?: string
  contact?: string
  contactPhone?: string
  couponId?: number
  district?: string
  orderCode: string
  userPhone?: string
}

// 响应接口
export interface SumbitReportOrderRes {
  data: {
    actuallyPrice: number
    discountsPrice: number
    orderCode: string
    orderId: number
    price: number
    priceUnit: string
    serverType: ServerType
    tempName: string
    tempType: number
    total: number
    totalPrice: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 提交订单
 * @param {object} params cmd
 * @param {number} params.couponId 优惠券id
 * @param {string} params.orderCode 订单Code
 * @param {string} params.userPhone 用户电话
 * @returns
 */
export function sumbitReportOrderApi(params: SumbitReportOrderParams) {
  return http.post<SumbitReportOrderRes>('/api/sumbitReportOrder', params)
}

// 参数接口
export interface PayReportOrderParams {
  fromTo?: FromPlatform
  orderCode: string
}

// 响应接口
export interface PayReportOrderRes {
  data: {
    currencyType: string
    fee: number
    isNeedToPay: boolean
    nonceStr: string
    packageValue: string
    paySign: string
    paymentArgs: Record<string, unknown>
    signType: string
    timeStamp: string
    version: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 支付订单，回传前端支付所需参数
 * @param {object} params cmd
 * @param {string} params.orderCode 订单编号
 * @returns
 */
export function payReportOrderApi(params: PayReportOrderParams) {
  return http.post<PayReportOrderRes>('/api/payReportOrder', params)
}

// 响应接口
export interface MemberLevelTempListRes {
  data: {
    capacity: number
    discountPrice: number
    flow: number
    originalPrice: number
    tempId: number
    tempName: string
    tempType: number
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}

/**
 * 会员等级模板列表
 * @returns
 */
export function memberLevelTempListApi() {
  return http.post<MemberLevelTempListRes>('/memberLevelTemp/memberLevelTempList')
}

// 参数接口
export interface CreateQrOrderParams {
  certificationId?: number
  fromTo?: FromPlatform
  number: number
  tempId: number
  userId: number
}
// 响应接口
export interface CreateQrOrderRes {
  data: {
    capacity: number
    flow: number
    number: number
    orderCode: string
    orderId: number
    price: number
    tempType: number
    totalPrice: number
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 创建订单
 * @param {object} params cmd
 * @param {number} params.certificationId 认证id，前期没有认证，可以不填
 * @param {number} params.fromTo 来源，1：微信小程序、2：抖音小程序、3：其他平台
 * @param {number} params.number 总数量
 * @param {number} params.tempId 会员等级模板ID
 * @param {number} params.userId 用户id
 * @returns
 */
export function createQrOrderApi(params: CreateQrOrderParams) {
  return http.post<CreateQrOrderRes>('/api/createQrOrder', params)
}

// 参数接口
export interface SumbitQrOrderParams {
  couponId?: number
  orderCode: string
}

// 响应接口
export interface SumbitQrOrderRes {
  data: {
    actuallyPrice: number
    capacity: number
    discountsPrice: number
    flow: number
    number: number
    orderCode: string
    orderId: number
    price: number
    tempType: number
    totalPrice: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 提交订单
 * @param {object} params cmd
 * @param {number} params.couponId 优惠券id
 * @param {string} params.orderCode 订单Code
 * @returns
 */
export function sumbitQrOrderApi(params: SumbitQrOrderParams) {
  return http.post<SumbitQrOrderRes>('/api/sumbitQrOrder', params)
}

// 参数接口
export interface PayQrOrderParams {
  orderCode: string
}

// 响应接口
export interface PayQrOrderRes {
  data: {
    currencyType: string
    fee: number
    isNeedToPay: boolean
    nonceStr: string
    packageValue: string
    paySign: string
    paymentArgs: Record<string, unknown>
    signType: string
    timeStamp: string
    version: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
export function payQrOrderApi(params: PayQrOrderParams) {
  return http.post<PayQrOrderRes>('/api/payQrOrder', params)
}

// 参数接口
export interface AllOrderPageParams {
  groupBy?: string
  isInvoiced?: number
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirection
  pageIndex?: number
  pageSize?: number
  payState?: number
  serverType?: ServerType
  userId: number
}

export interface AllOrderPageResData {
  actuallyPrice: number
  barOrderParamDTO: {
    endBarCode: string
    size: number
    startBarCode: string
  }
  createdDate: Record<string, unknown>
  invoiceId: number
  invoiceState: InvoiceState // 发票状态，1：已开票，0：未申请,2：已申请,-1:废票
  invoiceStateStr: string
  isHasOtherServer: boolean
  number: number
  orderCode: string
  orderId: number
  otherOrderParamDTO: {
    orderContent: string
  }
  payDate: Record<string, unknown>
  payState: number
  payStateStr: string
  price: number
  qrOrderParamDTO: {
    capacity: number
    certificationId: number
    flow: number
    tempName: string
    tempType: number
  }
  serverType: ServerType
  totalPrice: number
  transactionNo: string
  userId: number
}

// 响应接口
export interface AllOrderPageRes {
  data: AllOrderPageResData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 订单分页
 * @param {object} params qry
 * @param {string} params.groupBy
 * @param {number} params.isInvoiced 发票状态，1：已开票，0：未开票
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.payState 支付状态，-1:新建；0：未支付，1：已支付（支付成功），2：支付失败，
 * @param {number} params.serverType 服务类型：1：条码制作，2：信息上报,3:包装设计，4、标签设计，11：微站订单
 * @param {number} params.userId 用户id
 * @returns
 */
export function allOrderPageApi(params: AllOrderPageParams) {
  return http.post<AllOrderPageRes>('/api/allOrderPage', params)
}

export interface DownloadOrderInvoiceParamsOrderList {
  orderId: number
  serverType: ServerType
}
// 参数接口
export interface DownloadOrderInvoiceParams {
  address?: string
  bank?: string
  bankCode?: string
  companyName: string
  contact: string
  contactPhone: string
  creditCode: string
  downloadType: number
  email?: string
  invoiceTempId: number
  orderList: DownloadOrderInvoiceParamsOrderList[]
  phone?: string
  userId: number
}

// 响应接口
export interface DownloadOrderInvoiceRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 申请开发票
 * @param {object} params cmd
 * @param {string} params.address 注册地址
 * @param {string} params.bank 开户银行
 * @param {string} params.bankCode 银行账号
 * @param {string} params.companyName 企业名称,发票抬头,下单获取发票填写
 * @param {string} params.contact 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.creditCode 企业统一信用代码
 * @param {number} params.downloadType 下载方式:1:直接下载，2：发送到邮箱
 * @param {string} params.email 邮箱
 * @param {number} params.invoiceTempId 发票模版id
 * @param {Array} params.orderList 订单信息列表
 * @param {string} params.phone 注册电话
 * @param {number} params.userId 用户id
 * @returns
 */
export function downloadOrderInvoiceApi(params: DownloadOrderInvoiceParams) {
  return http.post<DownloadOrderInvoiceRes>('/api/downloadOrderInvoice', params)
}

// 参数接口
export interface UpdateOrderPhoneParams {
  orderCode: string
  phone: string
}

// 响应接口
export interface UpdateOrderPhoneRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 填写订单手机号
 * @param {object} params cmd
 * @param {string} params.orderCode 订单号
 * @param {string} params.phone 手机号
 * @returns
 */
export function updateOrderPhoneApi(params: UpdateOrderPhoneParams) {
  return http.post<UpdateOrderPhoneRes>('/api/updateOrderPhone', params)
}

// 参数接口
export interface AllReportOrderPageParams {
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirection
  pageIndex?: number
  pageSize?: number
  userId: number
}

export enum ReportStatus {
  dissatisfy = -1,
  processing = 0,
  success = 1,
}

// 响应接口
export interface AllReportOrderPageRes {
  data: {
    actuallyPrice: number
    isHasOtherServer: boolean
    orderCode: string
    orderContent: string
    orderId: number
    payDate: Record<string, unknown>
    reportStatus: ReportStatus
    reportStatusStr: string
    serverType: number
    userCode: string
    userId: number
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 通报订单分页
 * @param {object} params qry
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.userId 用户id
 * @returns
 */
export function allReportOrderPageApi(params: AllReportOrderPageParams) {
  return http.post<AllReportOrderPageRes>('/api/allReportOrderPage', params)
}

// 参数接口
export interface GetDouYinOrderPreDataParams {
  orderCode: string
  params?: string
  path: string
}

interface SkuLis {
  skuId: string
  price: number
  quantity: number
  title: string
  imageList: string[]
  type: number
  tagGroupId: string
}

interface OrderEntrySchem {
  path: string
}

export interface GetDouYinOrderPreDataJsonData {
  skuList: SkuLis[]
  outOrderNo: string
  totalAmount: number
  payExpireSeconds: number
  payNotifyUrl: string
  merchantUid: string
  orderEntrySchema: OrderEntrySchem
  limitPayWayList: number[]
}
// 响应接口
export interface GetDouYinOrderPreDataRes {
  data: {
    byteAuthorization: string
    data: string
    isZeroOrder: boolean
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 生成下单参数与签名
 * @param {object} params qry
 * @param {string} params.orderCode 订单号
 * @param {string} params.path 路径，例如：page/path/index
 * @returns
 */
export function getDouYinOrderPreDataApi(params: GetDouYinOrderPreDataParams) {
  return http.post<GetDouYinOrderPreDataRes>('/api/getDouYinOrderPreData', params)
}

// 参数接口
export interface GetDouYinOrderPayStateParams {
  orderCode: string
}

// 响应接口
export interface GetDouYinOrderPayStateRes {
  data: {
    orderCode: string
    orderId: number
    payState: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
export function getDouYinOrderPayStateApi(params: GetDouYinOrderPayStateParams) {
  return http.post<GetDouYinOrderPayStateRes>('/api/getDouYinOrderPayState', params)
}

// 参数接口
export interface ExpressInfoParams {
  orderCode: string
}

// 响应接口
export interface ExpressInfoRes {
  data: {
    addressDetail: string
    com_phone: string
    com_url: string
    company: string
    contact: string
    contactPhone: string
    courier_phone: string
    district: string
    list: {
      area_code: string
      area_location: string
      area_name: string
      context: string
      sub_status_detail: string
      time: string
    }[]
    status_desc: string
    status_detail: string
    sub_status_detail: string
    take_time: string
    tracks_count: string
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 查询快递信息
 * @param {object} params qry
 * @param {string} params.orderCode 订单Code
 * @returns
 */
export function expressInfoApi(params: ExpressInfoParams) {
  return http.post<ExpressInfoRes>('/api/expressInfo', params, {}, true)
}
