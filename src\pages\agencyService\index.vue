<route lang="json5">
{
  style: {
    navigationBarTitleText: '注册续展变更办理',
  },
}
</route>

<script lang="ts" setup>
import type { BaseProjectType } from '@/components/descriptionStr'
import { storeToRefs } from 'pinia'
import { PROJECT_3 } from '@/components/descriptionStr'
import { ServerType } from '@/enums'
import { useToPath } from '@/hooks/useToPath'
import { useUserStore } from '@/store/user'
import { toAgencyPath } from '@/utils'

const userStore = useUserStore()
const { existUncompletedRegister, existUncompletedRenewal, existUncompletedChange }
  = storeToRefs(userStore)
const { toPath } = useToPath()

function whichService(serviceTypeStr: BaseProjectType['descriptionServiceType']) {
  switch (serviceTypeStr) {
    case 'renewalService':
      if (existUncompletedRenewal.value) {
        return ServerType.renewalService
      }
      break
    case 'registerService':
      if (existUncompletedRegister.value) {
        return ServerType.registerService
      }
      break
    case 'modifyService':
      if (existUncompletedChange.value) {
        return ServerType.modifyService
      }
      break
  }
  return false
}
</script>

<template>
  <view class="p-4">
    <view class="mb-2 font-bold">
      请选择业务：
    </view>
    <view
      v-for="(item, index) in PROJECT_3"
      :key="index"
      class="mb-2 rd-2 bg-white py-2 pl-4 pr-2"
      @click="toPath(item, 'navigateTo')"
    >
      <view class="flex items-center gap-3">
        <view class="flex grow flex-col gap-1">
          <view class="text-base font-bold">
            {{ item.title }}
          </view>
          <view>
            <view
              class="o-tag o-color-primary float-left rd-1"
              :style="{ color: item.tagStrColor, background: item.tagBgColor }"
            >
              {{ item.typeStr }}
            </view>
          </view>
          <view class="o-color-aid text-xs">
            {{ item.description }}
          </view>
        </view>
        <view class="o-color-aid pr-1">
          <up-icon name="arrow-right" size="14" />
        </view>
      </view>
      <template v-if="whichService(item.descriptionServiceType)">
        <view class="o-line mb-4 mt-4" />
        <view
          class="o-bg-primary mt-3 flex flex-grow-1 items-baseline justify-center rd-2 py-1 color-white"
          @click.stop="toAgencyPath(whichService(item.descriptionServiceType))"
        >
          <text class="text-sm">
            您有未完成的业务，
          </text>
          <text class="font-bold">
            前往办理
          </text>
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
